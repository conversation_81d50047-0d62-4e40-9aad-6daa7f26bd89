name: "Copilot Setup Steps"

# Tự động chạy các setup steps khi có thay đổi để dễ dàng validation, và
# cho phép manual testing thông qua tab "Actions" của repository
on:
  workflow_dispatch:
  push:
    paths:
      - .github/workflows/copilot-setup-steps.yml
  pull_request:
    paths:
      - .github/workflows/copilot-setup-steps.yml

jobs:
  # Job BẮT BUỘC phải có tên `copilot-setup-steps` nếu không Copilot sẽ không nhận diện được.
  copilot-setup-steps:
    runs-on: ubuntu-latest

    # Đặt permissions ở mức thấp nhất có thể cần thiết cho các steps của bạn.
    # Copilot sẽ được cấp token riêng cho các operations của nó.
    permissions:
      # Nếu bạn muốn clone repository như một phần của setup steps,
      # ví dụ để install dependencies, bạn sẽ cần permission `contents: read`.
      # Nếu bạn không clone repository trong setup steps,
      # Copilot sẽ tự động làm điều này sau khi các steps hoàn thành.
      contents: read

    # Bạn có thể định nghĩa bất kỳ steps nào bạn muốn, và chúng sẽ chạy trước khi agent khởi động.
    # Nếu bạn không checkout code, Copilot sẽ làm điều này cho bạn.
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Java & Maven
        uses: actions/setup-java@v4
        with:
          java-version: "21"
          distribution: "temurin"
          cache: "maven"

      - uses: pnpm/action-setup@v4
        name: Install pnpm
        with:
          version: 10
          run_install: false

      - name: Install Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 20
          cache: 'pnpm'
          cache-dependency-path: src/frontend/pnpm-lock.yaml

      - name: Verify environment versions
        run: |
          echo "=== Environment Versions ==="
          echo "Java version:"
          java -version
          echo "Maven version:"
          mvn -version
          echo "Node version:"
          node -v
          echo "Pnpm version:"
          pnpm -v
          echo "==========================="

      - name: Install JavaScript dependencies
        # Tham số --frozen-lockfile đảm bảo rằng nếu có bất kỳ sự khác biệt nào giữa file lock và package.json, 
        # lệnh sẽ báo lỗi và không cập nhật file lock, giúp đảm bảo môi trường cài đặt luôn nhất quán với file lock đã commit
        working-directory: src/frontend
        run: pnpm install --frozen-lockfile
        if: hashFiles('src/frontend/package.json') != ''

      - name: Install Java dependencies
        working-directory: src/backend
        run: mvn clean compile -DskipTests
        if: hashFiles('src/backend/pom.xml') != ''
