name: Business Requirements Change Notification

on:
  issues:
    types: [opened]

concurrency:
  group: requirements-notification-${{ github.event.issue.number }}
  cancel-in-progress: true

jobs:
  notify-lark:
    name: Lark Requirements Change Notification
    runs-on: ubuntu-latest
    if: contains(github.event.issue.labels.*.name, 'yêu-cầu-nghiệp-vụ')
    
    steps:
      - name: Send Requirements Change notification to Lark
        env:
          # Cần cấu hình secret LARK_CHAT_GROUP_NOTIFICATION trong GitHub repository settings
          LARK_WEBHOOK_URL: ${{ secrets.LARK_CHAT_GROUP_NOTIFICATION }}
          # Cần cấu hình environment variable PROJECT_CODE
          PROJECT_CODE: ${{ vars.PROJECT_CODE }}
        run: |
          echo "🔔 Starting Lark Requirements Change notification process..."
          echo "Event: ${{ github.event_name }}"
          echo "Action: ${{ github.event.action }}"
          echo "Repository: ${{ github.repository }}"

          # Extract Issue information from GitHub context
          ISSUE_URL="${{ github.event.issue.html_url }}"
          ISSUE_TITLE="${{ github.event.issue.title }}"
          ISSUE_USER="${{ github.event.issue.user.login }}"
          ISSUE_CREATED_AT="${{ github.event.issue.created_at }}"
          ISSUE_NUMBER="${{ github.event.issue.number }}"
          REPO_NAME="${{ github.repository }}"
          WORKFLOW_URL="https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }}"

          echo "📋 Issue Details:"
          echo "  - URL: $ISSUE_URL"
          echo "  - Title: $ISSUE_TITLE"
          echo "  - User: $ISSUE_USER"
          echo "  - Number: $ISSUE_NUMBER"
          echo "  - Project Code: $PROJECT_CODE"

          # Format timestamp (GMT+7 timezone)
          FORMATTED_TIME=$(TZ='Asia/Ho_Chi_Minh' date -d "$ISSUE_CREATED_AT" +"%d/%m/%Y %H:%M:%S" 2>/dev/null || echo "$ISSUE_CREATED_AT")

          # Create notification content for requirements change with OSP prefix
          if [[ -n "$PROJECT_CODE" ]]; then
            PROJECT_PREFIX="[OSP][$PROJECT_CODE]"
          else
            PROJECT_PREFIX="[OSP][UNKNOWN]"
            echo "⚠️ PROJECT_CODE not configured, using UNKNOWN"
          fi
          
          EVENT_TYPE="$PROJECT_PREFIX [Requirements] $ISSUE_TITLE [Thay đổi mới]"
          CONTENT="Issue Link: [#$ISSUE_NUMBER]($ISSUE_URL)<br />Tạo bởi: $ISSUE_USER<br />Tạo lúc: $FORMATTED_TIME<br />Trạng thái: Cần đánh giá<br />Repository: $REPO_NAME<br />Workflow: [Xem chi tiết]($WORKFLOW_URL)<br /><br />📝 **Hành động cần thực hiện:**<br />• Đánh giá tác động thay đổi<br />• Phân tích rủi ro<br />• Cập nhật documentation<br />• Review implementation plan"

          echo "🎨 Creating JSON payload..."
          JSON_PAYLOAD=$(jq -n \
            --arg content "$CONTENT" \
            --arg eventType "$EVENT_TYPE" \
            '{
              msg_type: "interactive",
              card: {
                config: { wide_screen_mode: true },
                elements: [
                  { tag: "markdown", content: $content }
                ],
                header: { template: "orange", title: { content: $eventType, tag: "plain_text" } }
              }
            }')

          # Send notification to Lark
          if [[ -n "$LARK_WEBHOOK_URL" ]]; then
            echo "✅ Sending Requirements Change notification to Lark webhook..."
            curl -X POST \
              -H "Content-Type: application/json" \
              -d "$JSON_PAYLOAD" \
              "$LARK_WEBHOOK_URL"
            echo "✅ Requirements Change notification sent successfully!"
          else
            echo "❌ LARK_WEBHOOK_URL is not configured. Skipping notification."
          fi

          echo "🎉 Lark Requirements Change notification process completed!"
