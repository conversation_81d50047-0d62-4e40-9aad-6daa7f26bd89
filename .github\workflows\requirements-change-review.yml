name: "<PERSON><PERSON><PERSON> gi<PERSON> thay đổi yêu cầu nghiệp vụ"

on:
  push:
    branches:
      - main
      - develop
    paths:
      - 'docs/requirements/**'
  pull_request:
    types: [closed]
    branches:
      - main
      - develop
    paths:
      - 'docs/requirements/**'

permissions:
  contents: read
  issues: write
  pull-requests: write

jobs:
  create-requirements-review-issue:
    if: github.event_name == 'push' || (github.event_name == 'pull_request' && github.event.pull_request.merged == true)
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Get changed files
        id: changed-files
        run: |
          if [ "${{ github.event_name }}" == "push" ]; then
            PREVIOUS_COMMIT="${{ github.event.before }}"
            CURRENT_COMMIT="${{ github.sha }}"
          else
            # For merged PR
            PREVIOUS_COMMIT="${{ github.event.pull_request.base.sha }}"
            CURRENT_COMMIT="${{ github.event.pull_request.head.sha }}"
          fi
          
          echo "previous_commit=${PREVIOUS_COMMIT}" >> $GITHUB_OUTPUT
          echo "current_commit=${CURRENT_COMMIT}" >> $GITHUB_OUTPUT
          
          # Get list of changed files in docs/requirements
          CHANGED_FILES=$(git diff --name-only ${PREVIOUS_COMMIT}..${CURRENT_COMMIT} -- docs/requirements/ | head -20)
          
          if [ -z "$CHANGED_FILES" ]; then
            echo "No changes in docs/requirements directory"
            echo "has_changes=false" >> $GITHUB_OUTPUT
            exit 0
          fi
          
          echo "has_changes=true" >> $GITHUB_OUTPUT
          
          # Format changed files for display
          FORMATTED_FILES=""
          while IFS= read -r file; do
            if [ -n "$file" ]; then
              FORMATTED_FILES="${FORMATTED_FILES}- \`${file}\`\n"
            fi
          done <<< "$CHANGED_FILES"
          
          # Save multiline output
          {
            echo 'changed_files<<EOF'
            echo -e "$FORMATTED_FILES"
            echo 'EOF'
          } >> $GITHUB_OUTPUT

      - name: Get diff content
        id: get-diff
        if: steps.changed-files.outputs.has_changes == 'true'
        run: |
          PREVIOUS_COMMIT="${{ steps.changed-files.outputs.previous_commit }}"
          CURRENT_COMMIT="${{ steps.changed-files.outputs.current_commit }}"
          
          # Get diff for docs/requirements directory only
          DIFF_CONTENT=$(git diff ${PREVIOUS_COMMIT}..${CURRENT_COMMIT} -- docs/requirements/ | head -500)
          
          # Escape and format diff content for JSON
          DIFF_CONTENT_ESCAPED=$(echo "$DIFF_CONTENT" | sed 's/\\/\\\\/g' | sed 's/"/\\"/g' | sed ':a;N;$!ba;s/\n/\\n/g')
          
          # Truncate if too long and add notice
          if [ ${#DIFF_CONTENT_ESCAPED} -gt 10000 ]; then
            DIFF_CONTENT_ESCAPED="${DIFF_CONTENT_ESCAPED:0:10000}\\n\\n**[Nội dung diff quá dài, đã bị cắt ngắn. Vui lòng xem chi tiết trong commit.]**"
          fi
          
          {
            echo 'diff_content<<EOF'
            echo -e "$DIFF_CONTENT_ESCAPED"
            echo 'EOF'
          } >> $GITHUB_OUTPUT

      - name: Create issue from template
        if: steps.changed-files.outputs.has_changes == 'true'
        uses: actions/github-script@v7
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          script: |
            const previousCommit = '${{ steps.changed-files.outputs.previous_commit }}';
            const currentCommit = '${{ steps.changed-files.outputs.current_commit }}';
            const changedFiles = `${{ steps.changed-files.outputs.changed_files }}`;
            const diffContent = `${{ steps.get-diff.outputs.diff_content }}`;
            
            const currentDate = new Date().toLocaleDateString('vi-VN');
            const currentDateTime = new Date().toLocaleString('vi-VN');
            
            const previousCommitLink = `${context.payload.repository.html_url}/commit/${previousCommit}`;
            const currentCommitLink = `${context.payload.repository.html_url}/commit/${currentCommit}`;
            
            const committer = context.payload.head_commit?.committer?.name || 
                             context.payload.pull_request?.user?.login || 
                             'Unknown';
            
            const issueTitle = `[REVIEW] Đánh giá thay đổi yêu cầu nghiệp vụ - ${currentDate}`;
            
            // Create issue body with proper escaping
            const issueBody = [
              '## 📋 Thông tin thay đổi',
              '',
              `**Thời gian phát hiện:** ${currentDateTime}`,
              `**Người commit:** ${committer}`,
              '',
              '> 🤖 **@copilot** và **Jules**: Vui lòng đánh giá tác động của các thay đổi yêu cầu nghiệp vụ này.',
              '',
              '## Nội dung thay đổi',
              '',
              `**Commit trước:** \`${previousCommit.substring(0, 7)}\` ([Xem chi tiết](${previousCommitLink}))`,
              `**Commit sau:** \`${currentCommit.substring(0, 7)}\` ([Xem chi tiết](${currentCommitLink}))`,
              '',
              '### Tệp tin bị thay đổi:',
              changedFiles,
              '',
              '### Chi tiết thay đổi:',
              '```diff',
              diffContent,
              '```',
              '',
              '## 🔍 Cần đánh giá',
              '',
              '### 1. Loại thay đổi',
              '- [ ] Tính năng mới',
              '- [ ] Sửa đổi tính năng cũ',
              '- [ ] Chưa phân loại',
              '',
              '### 2. Phân tích tác động',
              '**Thay đổi này sẽ ảnh hưởng đến những user story/epic nào?**',
              '',
              '_[Vui lòng điền thông tin]_',
              '',
              '### 3. Công việc cần thực hiện theo vai trò',
              '',
              '#### 👨‍🎨 Designer',
              '- [ ] Cần cập nhật UI/UX design',
              '- [ ] Cần cập nhật wireframe',
              '- [ ] Cần tạo prototype mới',
              '- [ ] Không cần thay đổi',
              '',
              '**Chi tiết công việc:**',
              '_[Vui lòng mô tả cụ thể]_',
              '',
              '#### 📊 System Analyst (SA)',
              '- [ ] Cần cập nhật tài liệu phân tích',
              '- [ ] Cần xem xét lại use case',
              '- [ ] Cần cập nhật business rules',
              '- [ ] Không cần thay đổi',
              '',
              '**Chi tiết công việc:**',
              '_[Vui lòng mô tả cụ thể]_',
              '',
              '#### 💻 Developer',
              '- [ ] Cần cập nhật code',
              '- [ ] Cần thay đổi API',
              '- [ ] Cần cập nhật database schema',
              '- [ ] Không cần thay đổi',
              '',
              '**Chi tiết công việc:**',
              '_[Vui lòng mô tả cụ thể]_',
              '',
              '#### 🧪 Tester',
              '- [ ] Cần cập nhật test case',
              '- [ ] Cần cập nhật test plan',
              '- [ ] Cần viết automation test mới',
              '- [ ] Không cần thay đổi',
              '',
              '**Chi tiết công việc:**',
              '_[Vui lòng mô tả cụ thể]_',
              '',
              '### 4. Mức độ ưu tiên',
              '- [ ] Thấp',
              '- [ ] Trung bình',
              '- [ ] Cao',
              '- [ ] Khẩn cấp',
              '',
              '### 5. Bước tiếp theo',
              '- [ ] Schedule meeting với stakeholders',
              '- [ ] Review và approve design changes',
              '- [ ] Cập nhật development plan',
              '- [ ] Thực hiện testing',
              '- [ ] Khác: _[Vui lòng mô tả]_',
              '',
              '### 6. Ghi chú bổ sung',
              '_[Thông tin bổ sung hoặc các rủi ro cần lưu ý]_',
              '',
              '---',
              '**🤖 Issue này được tạo tự động bởi GitHub Actions**',
              '',
              '**Assignees:**',
              '- 🤖 **Copilot**: Thực hiện phân tích tự động và đưa ra gợi ý',
              '- 🔍 **Jules**: Đánh giá và xác thực kết quả từ AI'
            ].join('\n');

            const issue = await github.rest.issues.create({
              owner: context.repo.owner,
              repo: context.repo.repo,
              title: issueTitle,
              body: issueBody,
              labels: ['yêu-cầu-nghiệp-vụ', 'requirements-review', 'business-analysis', 'auto-generated', 'assign-to-copilot', 'assign-to-jules']
            });
            
            console.log(`Created issue: ${issue.data.html_url}`);

      - name: Add comment to PR (if applicable)
        if: github.event_name == 'pull_request' && steps.changed-files.outputs.has_changes == 'true'
        uses: actions/github-script@v7
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          script: |
            const comment = [
              '## 🔔 Thông báo: Phát hiện thay đổi yêu cầu nghiệp vụ',
              '',
              'Hệ thống đã phát hiện có thay đổi trong thư mục `docs/requirements`.',
              '',
              'Một issue đánh giá tác động đã được tạo tự động để review những thay đổi này:',
              '- 📋 **Issue:** Sẽ được tạo sau khi PR được merge',
              '- 🎯 **Mục đích:** Đánh giá tác động đến các user story/epic và phân công công việc cho các role',
              '- ⏰ **Thời gian:** Ngay sau khi merge',
              '',
              '**Lưu ý:** Vui lòng kiểm tra và xử lý issue review sau khi merge PR này.'
            ].join('\n');

            await github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: comment
            });
