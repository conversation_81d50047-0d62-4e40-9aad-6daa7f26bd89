### **1. <PERSON><PERSON><PERSON><PERSON> <PERSON>ắ<PERSON>**

* **<PERSON><PERSON><PERSON> ngữ:**
    * <PERSON><PERSON> dụng **tiếng Việt** cho tất cả các phản hồi.
    * Giữ nguyên các thuật ngữ kỹ thuật **tiếng Anh** thuộc chuyên ngành Công nghệ thông tin và Phát triển phần mềm.
* **Phong cách:** Tạo phản hồi ngắn gọn, đơn giản và đi thẳng vào vấn đề.
* **Phạm vi:** Chỉ thực hiện và tạo tài liệu kiểm thử (testing) khi có yêu cầu cụ thể.
* **Lưu trữ:** Tất cả tài liệu phải được lưu dưới định dạng **Markdown** trong thư mục `/docs`.

### **2. <PERSON><PERSON><PERSON> cầu Dự án**

#### **2.1. <PERSON><PERSON><PERSON> trúc Dự án**

Dự án được tổ chức theo kiến trúc monorepo:
```
src/
├── backend/          # Java Spring Boot backend
└── frontend/         # TypeScript React frontend
    ├── apps/         # Applications
    │   ├── admin/    # Admin dashboard (NextJS)
    │   └── web/      # Public web app (NextJS)
    └── packages/     # Shared packages
        ├── api-client/     # API client
        ├── auth/           # Authentication
        ├── eslint-config/  # ESLint configurations
        ├── lib/            # Shared utilities
        ├── store/          # State management (Zustand)
        ├── types/          # TypeScript types
        ├── typescript-config/  # TypeScript configurations
        └── ui/             # UI components library
```

#### **2.2. Công nghệ và Kiến trúc**

**Backend:**
* **Framework:** Spring Boot 1.2.7 ⚠️ **KHÔNG TƯƠNG THÍCH** với Java 21
* **Java Version:** Java 21 (cần hạ xuống Java 17 hoặc nâng Spring Boot >= 3.2)
* **Database:** H2 (development), PostgreSQL (production)
* **Build Tool:** Maven
* **Libraries:** Lombok, Log4JDBC

> **⚠️ Lưu ý tương thích:** Spring Boot 1.2.7 (2015) chỉ hỗ trợ Java 8. Để sử dụng Java 21, cần nâng cấp Spring Boot lên phiên bản >= 3.2 hoặc hạ Java xuống Java 17.

**Frontend:**
* **Framework:** NextJS 15.2.3, React 19.0.0
* **Language:** TypeScript 5.7.3
* **Styling:** TailwindCSS 4.0.8, Shadcn/UI components
* **State Management:** Zustand 5.0.6
* **Package Manager:** PNPM 10.4.1
* **Build Tool:** Turbo (monorepo)
* **UI Components:** Radix UI, Lucide React
* **Validation:** Zod

**CI/CD và Infrastructure:**
* **CI/CD:** Github Actions (Copilot integration)
* **Node Version:** 20
* **Java Build:** Maven

#### **2.3. Nguyên tắc Phát triển**

Áp dụng nghiêm ngặt các nguyên tắc sau cho TOÀN BỘ công nghệ và kiến trúc đã nêu:
* **Kiến trúc & Thiết kế:** Clean Architecture, Domain-Driven Design (DDD).
* **Lập trình:** Tuân thủ các nguyên tắc **Clean Code**, **SOLID**, **DRY**, **KISS**, và **YAGNI**.
* **Tiêu chuẩn:** Luôn tuân thủ `coding convention`, `coding guidelines`, và `best practices` của từng ngôn ngữ và framework.
    * **Nguồn tham chiếu ưu tiên:** Ưu tiên tham khảo và áp dụng theo **Google Style Guide** cho các ngôn ngữ được hỗ trợ (ví dụ: Java, TypeScript, Markdown).

#### **2.4. Yêu cầu Hệ thống**

Hệ thống phải đảm bảo các tiêu chí sau:
* Hiệu năng cao.
* An toàn và bảo mật.
* Khả năng mở rộng (Scalability).
* Khả năng bảo trì (Maintainability).

#### **2.5. Commands Hữu ích**

**Backend (Maven):**
```bash
cd src/backend
mvn clean compile -DskipTests    # Build without tests
mvn clean install               # Full build with tests
mvn spring-boot:run            # Run application
```

**Frontend (PNPM):**
```bash
cd src/frontend
pnpm install --frozen-lockfile  # Install dependencies
pnpm dev                        # Start development servers
pnpm build                      # Build all apps
pnpm lint                       # Lint all packages
pnpm typecheck                  # Type checking
```

### **3. Quy trình Xử lý**

#### **3.1. Quản lý Hướng dẫn (Guidelines)**

1.  **Lưu trữ:** Tạo file hướng dẫn chi tiết trong thư mục `/docs/guidelines/`.
2.  **Cập nhật tóm tắt:** Thêm một mục mới vào file `/docs/guidelines/summary.md` với các trường: `tên hướng dẫn`, `mô tả tóm tắt`, `tag`.

#### **3.2. Xử lý Lỗi (Issues)**

Khi nhận được yêu cầu sửa lỗi, hãy thực hiện theo quy trình sau:

1.  **Tra cứu:** Tìm kiếm trong file `/docs/issues/summary.md` bằng `tag` để tìm các lỗi tương tự đã giải quyết.
2.  **Tham khảo:** Nếu tìm thấy, sử dụng giải pháp chi tiết của lỗi đó làm tài liệu tham khảo.
3.  **Lập tài liệu:**
    * Tạo một file Markdown mới trong `/docs/issues/` cho lỗi hiện tại.
    * Nội dung file phải bao gồm: **Phân tích lỗi**, **Giải pháp**, và **Kết quả**.
4.  **Cập nhật tóm tắt:** Thêm một mục mới vào file `/docs/issues/summary.md` với các trường: `tên lỗi`, `mô tả tóm tắt`, `tag`.