# Component Architecture Design

## 1. Overview

Tài liệu này mô tả chi tiết kiến trúc từng component trong hệ thống, bao gồm frontend components, backend services, và shared libraries.

## 2. Frontend Component Architecture

### 2.1. NextJS Applications Structure

#### Admin Application (`apps/admin/`)
```
apps/admin/
├── app/                    # NextJS 15 App Router
│   ├── auth/
│   │   ├── login/
│   │   │   └── page.tsx    # Login page component
│   │   └── layout.tsx      # Auth layout
│   ├── dashboard/
│   │   └── page.tsx        # Dashboard page component
│   ├── users/
│   │   ├── page.tsx        # Users list page
│   │   ├── create/
│   │   │   └── page.tsx    # Create user page
│   │   └── [id]/
│   │       └── edit/
│   │           └── page.tsx # Edit user page
│   ├── content/
│   │   ├── page.tsx        # Content list page
│   │   ├── create/
│   │   │   └── page.tsx    # Create content page
│   │   └── [id]/
│   │       └── edit/
│   │           └── page.tsx # Edit content page
│   ├── layout.tsx          # Root layout
│   └── page.tsx           # Admin home page
├── components/             # App-specific components
│   ├── forms/
│   │   ├── LoginForm.tsx
│   │   ├── UserForm.tsx
│   │   └── ContentForm.tsx
│   ├── tables/
│   │   ├── UsersTable.tsx
│   │   └── ContentTable.tsx
│   └── modals/
│       ├── ConfirmModal.tsx
│       └── UserModal.tsx
├── hooks/                  # Custom hooks
│   ├── useAuth.ts
│   ├── useUsers.ts
│   └── useContent.ts
├── services/               # API service layer
│   ├── authService.ts
│   ├── userService.ts
│   └── contentService.ts
├── stores/                 # Zustand stores
│   ├── authStore.ts
│   ├── userStore.ts
│   └── contentStore.ts
├── types/                  # App-specific types
│   └── index.ts
└── lib/
    └── utils.ts           # App-specific utilities
```

#### Web Application (`apps/web/`)
```
apps/web/
├── app/                    # NextJS 15 App Router
│   ├── blog/
│   │   ├── page.tsx        # Blog list page
│   │   └── [slug]/
│   │       └── page.tsx    # Blog post detail
│   ├── about/
│   │   └── page.tsx        # About page
│   ├── contact/
│   │   └── page.tsx        # Contact page
│   ├── layout.tsx          # Root layout
│   └── page.tsx           # Home page
├── components/             # App-specific components
│   ├── Header.tsx
│   ├── Footer.tsx
│   ├── BlogCard.tsx
│   └── ContactForm.tsx
├── hooks/                  # Custom hooks
│   ├── useContent.ts
│   └── useSearch.ts
├── services/               # API service layer
│   └── contentService.ts
└── stores/                 # Zustand stores
    └── contentStore.ts
```

### 2.2. Shared UI Components (`packages/ui/`)

#### Component Hierarchy
```
packages/ui/src/components/
├── atoms/                  # Atomic components
│   ├── Button/
│   │   ├── Button.tsx
│   │   ├── Button.stories.tsx
│   │   └── Button.test.tsx
│   ├── Input/
│   │   ├── Input.tsx
│   │   ├── Input.stories.tsx
│   │   └── Input.test.tsx
│   ├── Label/
│   ├── Avatar/
│   ├── Badge/
│   ├── Skeleton/
│   └── Spinner/
├── molecules/              # Molecular components
│   ├── FormField/
│   │   ├── FormField.tsx
│   │   ├── FormField.stories.tsx
│   │   └── FormField.test.tsx
│   ├── SearchBox/
│   ├── UserCard/
│   ├── Dropdown/
│   ├── Pagination/
│   └── BreadCrumb/
├── organisms/              # Organism components
│   ├── Header/
│   │   ├── Header.tsx
│   │   ├── Header.stories.tsx
│   │   └── Header.test.tsx
│   ├── Sidebar/
│   ├── DataTable/
│   ├── ContentEditor/
│   ├── FileUploader/
│   └── NavigationMenu/
├── templates/              # Template components
│   ├── DashboardLayout/
│   │   ├── DashboardLayout.tsx
│   │   └── DashboardLayout.test.tsx
│   ├── AuthLayout/
│   ├── PublicLayout/
│   └── ErrorLayout/
└── providers/              # Context providers
    ├── ThemeProvider/
    ├── ToastProvider/
    └── ModalProvider/
```

#### Component Design Pattern
```typescript
// Example: Button Component
interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  /** Button variant style */
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';
  /** Button size */
  size?: 'default' | 'sm' | 'lg' | 'icon';
  /** Render as child element */
  asChild?: boolean;
  /** Loading state */
  loading?: boolean;
  /** Icon to display */
  icon?: React.ReactNode;
}

/**
 * Component Button có thể tùy chỉnh với nhiều variant và size khác nhau
 * Hỗ trợ các accessibility features và có thể render thành element khác thông qua asChild
 */
const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant = 'default', size = 'default', asChild = false, loading, icon, children, ...props }, ref) => {
    const Comp = asChild ? Slot : 'button';
    
    return (
      <Comp
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        disabled={loading || props.disabled}
        {...props}
      >
        {loading && <Spinner size="sm" />}
        {!loading && icon && icon}
        {children}
      </Comp>
    );
  }
);

Button.displayName = 'Button';
```

### 2.3. Shared Libraries Architecture

#### API Client (`packages/api-client/`)
```typescript
// Base API client configuration
class ApiClient {
  private instance: AxiosInstance;
  
  constructor(baseURL: string) {
    this.instance = axios.create({
      baseURL,
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
      },
    });
    
    this.setupInterceptors();
  }
  
  private setupInterceptors() {
    // Request interceptor - add auth token
    this.instance.interceptors.request.use((config) => {
      const token = getAuthToken();
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
      return config;
    });
    
    // Response interceptor - handle errors
    this.instance.interceptors.response.use(
      (response) => response,
      (error) => {
        if (error.response?.status === 401) {
          // Handle unauthorized
          handleUnauthorized();
        }
        return Promise.reject(error);
      }
    );
  }
}

// Resource-specific API clients
export class UserApiClient extends ApiClient {
  async getUsers(params?: UserListParams): Promise<ApiResponse<User[]>> {
    const response = await this.instance.get('/api/users', { params });
    return response.data;
  }
  
  async createUser(userData: CreateUserData): Promise<ApiResponse<User>> {
    const response = await this.instance.post('/api/users', userData);
    return response.data;
  }
  
  async updateUser(id: number, userData: UpdateUserData): Promise<ApiResponse<User>> {
    const response = await this.instance.put(`/api/users/${id}`, userData);
    return response.data;
  }
  
  async deleteUser(id: number): Promise<ApiResponse<void>> {
    const response = await this.instance.delete(`/api/users/${id}`);
    return response.data;
  }
}
```

#### Authentication Library (`packages/auth/`)
```typescript
// JWT token management
export class TokenManager {
  private static readonly ACCESS_TOKEN_KEY = 'access_token';
  private static readonly REFRESH_TOKEN_KEY = 'refresh_token';
  
  static setTokens(accessToken: string, refreshToken: string): void {
    if (typeof window !== 'undefined') {
      localStorage.setItem(this.ACCESS_TOKEN_KEY, accessToken);
      localStorage.setItem(this.REFRESH_TOKEN_KEY, refreshToken);
    }
  }
  
  static getAccessToken(): string | null {
    if (typeof window !== 'undefined') {
      return localStorage.getItem(this.ACCESS_TOKEN_KEY);
    }
    return null;
  }
  
  static clearTokens(): void {
    if (typeof window !== 'undefined') {
      localStorage.removeItem(this.ACCESS_TOKEN_KEY);
      localStorage.removeItem(this.REFRESH_TOKEN_KEY);
    }
  }
  
  static isTokenExpired(token: string): boolean {
    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      return payload.exp * 1000 < Date.now();
    } catch {
      return true;
    }
  }
}

// Auth context và hooks
interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (credentials: LoginCredentials) => Promise<void>;
  logout: () => void;
  refreshToken: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | null>(null);

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within AuthProvider');
  }
  return context;
};
```

#### State Management (`packages/store/`)
```typescript
// Base store interface
interface BaseStore {
  isLoading: boolean;
  error: string | null;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
}

// Create base store với common functionality
export const createBaseStore = <T extends BaseStore>(
  initialState: Omit<T, keyof BaseStore>
): T & BaseStore => {
  return create<T & BaseStore>((set) => ({
    ...initialState,
    isLoading: false,
    error: null,
    setLoading: (loading: boolean) => set({ isLoading: loading }),
    setError: (error: string | null) => set({ error }),
  }));
};

// User store implementation
interface UserStore extends BaseStore {
  users: User[];
  currentUser: User | null;
  fetchUsers: () => Promise<void>;
  createUser: (userData: CreateUserData) => Promise<void>;
  updateUser: (id: number, userData: UpdateUserData) => Promise<void>;
  deleteUser: (id: number) => Promise<void>;
}

export const useUserStore = create<UserStore>((set, get) => ({
  // Base store properties
  isLoading: false,
  error: null,
  setLoading: (loading: boolean) => set({ isLoading: loading }),
  setError: (error: string | null) => set({ error }),
  
  // User-specific state
  users: [],
  currentUser: null,
  
  // Actions
  fetchUsers: async () => {
    const { setLoading, setError } = get();
    try {
      setLoading(true);
      setError(null);
      
      const response = await userApi.getUsers();
      set({ users: response.data });
    } catch (error) {
      setError(error instanceof Error ? error.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  },
  
  createUser: async (userData: CreateUserData) => {
    const { setLoading, setError, fetchUsers } = get();
    try {
      setLoading(true);
      setError(null);
      
      await userApi.createUser(userData);
      await fetchUsers(); // Refresh list
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to create user');
      throw error;
    } finally {
      setLoading(false);
    }
  },
}));
```

#### Types Library (`packages/types/`)
```typescript
// Base types
export interface BaseEntity {
  id: number;
  createdAt: string;
  updatedAt: string;
}

// User-related types
export interface User extends BaseEntity {
  email: string;
  firstName?: string;
  lastName?: string;
  active: boolean;
  roles: Role[];
}

export interface Role extends BaseEntity {
  name: string;
  description?: string;
}

export interface CreateUserData {
  email: string;
  password: string;
  firstName?: string;
  lastName?: string;
  roleIds: number[];
}

export interface UpdateUserData {
  firstName?: string;
  lastName?: string;
  active?: boolean;
  roleIds?: number[];
}

// Content-related types
export interface Content extends BaseEntity {
  title: string;
  content: string;
  status: ContentStatus;
  slug: string;
  authorId: number;
  author: User;
  publishedAt?: string;
}

export enum ContentStatus {
  DRAFT = 'DRAFT',
  PUBLISHED = 'PUBLISHED',
  ARCHIVED = 'ARCHIVED',
}

// API response types
export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
  errors?: ApiError[];
  timestamp: string;
}

export interface ApiError {
  field?: string;
  message: string;
}

// Form validation types
export interface ValidationError {
  field: string;
  message: string;
}

// Pagination types
export interface PaginationParams {
  page: number;
  size: number;
  sort?: string;
  direction?: 'asc' | 'desc';
}

export interface PaginatedResponse<T> {
  content: T[];
  totalElements: number;
  totalPages: number;
  currentPage: number;
  pageSize: number;
  hasNext: boolean;
  hasPrevious: boolean;
}
```

## 3. Backend Service Architecture

### 3.1. Spring Boot Application Structure

```
src/main/java/com/example/
├── App.java                    # Main application class
├── config/                     # Configuration classes
│   ├── AppConfig.java         # General configuration
│   ├── SecurityConfig.java    # Security configuration
│   ├── DatabaseConfig.java    # Database configuration
│   └── WebConfig.java         # Web MVC configuration
├── controller/                 # REST controllers
│   ├── AuthController.java
│   ├── UserController.java
│   ├── ContentController.java
│   └── FileController.java
├── service/                    # Business logic services
│   ├── AuthService.java
│   ├── UserService.java
│   ├── ContentService.java
│   └── FileService.java
├── repository/                 # Data access repositories
│   ├── UserRepository.java
│   ├── ContentRepository.java
│   ├── RoleRepository.java
│   └── FileRepository.java
├── entity/                     # JPA entities
│   ├── User.java
│   ├── Content.java
│   ├── Role.java
│   └── File.java
├── dto/                        # Data Transfer Objects
│   ├── request/
│   │   ├── LoginRequest.java
│   │   ├── CreateUserRequest.java
│   │   └── CreateContentRequest.java
│   └── response/
│       ├── AuthResponse.java
│       ├── UserResponse.java
│       └── ContentResponse.java
├── mapper/                     # Entity-DTO mappers
│   ├── UserMapper.java
│   └── ContentMapper.java
├── exception/                  # Custom exceptions
│   ├── BusinessException.java
│   ├── ValidationException.java
│   └── ResourceNotFoundException.java
├── security/                   # Security components
│   ├── JwtAuthenticationFilter.java
│   ├── JwtTokenProvider.java
│   └── UserDetailsServiceImpl.java
└── util/                       # Utility classes
    ├── Constants.java
    └── ValidationUtils.java
```

### 3.2. Controller Layer Design

```java
/**
 * REST Controller cho User management operations
 * Cung cấp endpoints để CRUD users và role management
 */
@RestController
@RequestMapping("/api/users")
@Validated
@Slf4j
public class UserController {
    
    private final UserService userService;
    private final UserMapper userMapper;
    
    public UserController(UserService userService, UserMapper userMapper) {
        this.userService = userService;
        this.userMapper = userMapper;
    }
    
    /**
     * Lấy danh sách users với pagination và filtering
     */
    @GetMapping
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Page<UserResponse>>> getUsers(
            @Valid @ModelAttribute UserListRequest request,
            Pageable pageable
    ) {
        log.info("Fetching users with filters: {}", request);
        
        Page<User> users = userService.findUsers(request, pageable);
        Page<UserResponse> userResponses = users.map(userMapper::toResponse);
        
        return ResponseEntity.ok(ApiResponse.success(userResponses));
    }
    
    /**
     * Tạo user mới
     */
    @PostMapping
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<UserResponse>> createUser(
            @Valid @RequestBody CreateUserRequest request
    ) {
        log.info("Creating new user with email: {}", request.getEmail());
        
        User user = userService.createUser(request);
        UserResponse response = userMapper.toResponse(user);
        
        return ResponseEntity.status(HttpStatus.CREATED)
            .body(ApiResponse.success(response, "User created successfully"));
    }
    
    /**
     * Cập nhật user information
     */
    @PutMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN') or #id == authentication.principal.id")
    public ResponseEntity<ApiResponse<UserResponse>> updateUser(
            @PathVariable Long id,
            @Valid @RequestBody UpdateUserRequest request
    ) {
        log.info("Updating user with id: {}", id);
        
        User user = userService.updateUser(id, request);
        UserResponse response = userMapper.toResponse(user);
        
        return ResponseEntity.ok(ApiResponse.success(response, "User updated successfully"));
    }
    
    /**
     * Xóa user
     */
    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Void>> deleteUser(@PathVariable Long id) {
        log.info("Deleting user with id: {}", id);
        
        userService.deleteUser(id);
        
        return ResponseEntity.ok(ApiResponse.success(null, "User deleted successfully"));
    }
}
```

### 3.3. Service Layer Design

```java
/**
 * Service class chứa business logic cho User operations
 * Implements user management, validation và business rules
 */
@Service
@Transactional
@Slf4j
public class UserService {
    
    private final UserRepository userRepository;
    private final RoleRepository roleRepository;
    private final PasswordEncoder passwordEncoder;
    private final UserMapper userMapper;
    
    public UserService(
            UserRepository userRepository,
            RoleRepository roleRepository,
            PasswordEncoder passwordEncoder,
            UserMapper userMapper
    ) {
        this.userRepository = userRepository;
        this.roleRepository = roleRepository;
        this.passwordEncoder = passwordEncoder;
        this.userMapper = userMapper;
    }
    
    /**
     * Tìm users với filtering và pagination
     */
    @Transactional(readOnly = true)
    public Page<User> findUsers(UserListRequest request, Pageable pageable) {
        Specification<User> spec = UserSpecification.withFilters(request);
        return userRepository.findAll(spec, pageable);
    }
    
    /**
     * Tạo user mới với validation
     */
    public User createUser(CreateUserRequest request) {
        // Validate email uniqueness
        if (userRepository.existsByEmail(request.getEmail())) {
            throw new BusinessException("Email already exists: " + request.getEmail());
        }
        
        // Validate roles
        Set<Role> roles = validateAndGetRoles(request.getRoleIds());
        
        // Create user entity
        User user = User.builder()
            .email(request.getEmail())
            .passwordHash(passwordEncoder.encode(request.getPassword()))
            .firstName(request.getFirstName())
            .lastName(request.getLastName())
            .active(true)
            .roles(roles)
            .build();
        
        User savedUser = userRepository.save(user);
        log.info("Created user with id: {} and email: {}", savedUser.getId(), savedUser.getEmail());
        
        return savedUser;
    }
    
    /**
     * Cập nhật user information
     */
    public User updateUser(Long id, UpdateUserRequest request) {
        User user = findUserById(id);
        
        // Update basic information
        if (request.getFirstName() != null) {
            user.setFirstName(request.getFirstName());
        }
        if (request.getLastName() != null) {
            user.setLastName(request.getLastName());
        }
        if (request.getActive() != null) {
            user.setActive(request.getActive());
        }
        
        // Update roles if provided
        if (request.getRoleIds() != null && !request.getRoleIds().isEmpty()) {
            Set<Role> roles = validateAndGetRoles(request.getRoleIds());
            user.setRoles(roles);
        }
        
        User savedUser = userRepository.save(user);
        log.info("Updated user with id: {}", savedUser.getId());
        
        return savedUser;
    }
    
    /**
     * Soft delete user
     */
    public void deleteUser(Long id) {
        User user = findUserById(id);
        
        // Soft delete by deactivating
        user.setActive(false);
        userRepository.save(user);
        
        log.info("Soft deleted user with id: {}", id);
    }
    
    /**
     * Helper method để tìm user by ID với error handling
     */
    @Transactional(readOnly = true)
    public User findUserById(Long id) {
        return userRepository.findById(id)
            .orElseThrow(() -> new ResourceNotFoundException("User not found with id: " + id));
    }
    
    /**
     * Validate và get roles from IDs
     */
    private Set<Role> validateAndGetRoles(Set<Long> roleIds) {
        Set<Role> roles = new HashSet<>(roleRepository.findAllById(roleIds));
        
        if (roles.size() != roleIds.size()) {
            throw new BusinessException("Some role IDs are invalid");
        }
        
        return roles;
    }
}
```

### 3.4. Repository Layer Design

```java
/**
 * JPA Repository cho User entity
 * Cung cấp data access methods và custom queries
 */
@Repository
public interface UserRepository extends JpaRepository<User, Long>, JpaSpecificationExecutor<User> {
    
    /**
     * Tìm user theo email
     */
    Optional<User> findByEmail(String email);
    
    /**
     * Kiểm tra email đã tồn tại
     */
    boolean existsByEmail(String email);
    
    /**
     * Tìm users theo active status
     */
    List<User> findByActive(boolean active);
    
    /**
     * Tìm users theo role name
     */
    @Query("SELECT u FROM User u JOIN u.roles r WHERE r.name = :roleName")
    List<User> findByRoleName(@Param("roleName") String roleName);
    
    /**
     * Tìm users với custom query và pagination
     */
    @Query("SELECT u FROM User u WHERE " +
           "(:email IS NULL OR u.email LIKE %:email%) AND " +
           "(:active IS NULL OR u.active = :active)")
    Page<User> findUsersWithFilters(
            @Param("email") String email,
            @Param("active") Boolean active,
            Pageable pageable
    );
    
    /**
     * Count users by role
     */
    @Query("SELECT COUNT(u) FROM User u JOIN u.roles r WHERE r.name = :roleName")
    long countByRoleName(@Param("roleName") String roleName);
}

/**
 * Specification class cho dynamic queries
 */
@UtilityClass
public class UserSpecification {
    
    public static Specification<User> withFilters(UserListRequest request) {
        return Specification.where(hasEmail(request.getEmail()))
            .and(hasActive(request.getActive()))
            .and(hasRole(request.getRoleName()));
    }
    
    private static Specification<User> hasEmail(String email) {
        return (root, query, criteriaBuilder) -> {
            if (email == null || email.trim().isEmpty()) {
                return null;
            }
            return criteriaBuilder.like(
                criteriaBuilder.lower(root.get("email")),
                "%" + email.toLowerCase() + "%"
            );
        };
    }
    
    private static Specification<User> hasActive(Boolean active) {
        return (root, query, criteriaBuilder) -> {
            if (active == null) {
                return null;
            }
            return criteriaBuilder.equal(root.get("active"), active);
        };
    }
    
    private static Specification<User> hasRole(String roleName) {
        return (root, query, criteriaBuilder) -> {
            if (roleName == null || roleName.trim().isEmpty()) {
                return null;
            }
            Join<User, Role> roleJoin = root.join("roles");
            return criteriaBuilder.equal(roleJoin.get("name"), roleName);
        };
    }
}
```

### 3.5. Entity Design

```java
/**
 * JPA Entity cho User table
 * Định nghĩa user model với relationships và constraints
 */
@Entity
@Table(name = "users")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class User extends BaseEntity {
    
    @Column(name = "email", nullable = false, unique = true, length = 255)
    @Email(message = "Email format is invalid")
    private String email;
    
    @Column(name = "password_hash", nullable = false)
    @JsonIgnore
    private String passwordHash;
    
    @Column(name = "first_name", length = 100)
    private String firstName;
    
    @Column(name = "last_name", length = 100)
    private String lastName;
    
    @Column(name = "active", nullable = false)
    @Builder.Default
    private Boolean active = true;
    
    /**
     * Many-to-many relationship với Role entity
     */
    @ManyToMany(fetch = FetchType.LAZY, cascade = {CascadeType.PERSIST, CascadeType.MERGE})
    @JoinTable(
        name = "user_roles",
        joinColumns = @JoinColumn(name = "user_id"),
        inverseJoinColumns = @JoinColumn(name = "role_id")
    )
    @Builder.Default
    private Set<Role> roles = new HashSet<>();
    
    /**
     * One-to-many relationship với Content entity
     */
    @OneToMany(mappedBy = "author", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @Builder.Default
    private Set<Content> contents = new HashSet<>();
    
    /**
     * Helper method để add role
     */
    public void addRole(Role role) {
        roles.add(role);
        role.getUsers().add(this);
    }
    
    /**
     * Helper method để remove role
     */
    public void removeRole(Role role) {
        roles.remove(role);
        role.getUsers().remove(this);
    }
    
    /**
     * Check if user has specific role
     */
    public boolean hasRole(String roleName) {
        return roles.stream()
            .anyMatch(role -> role.getName().equals(roleName));
    }
    
    /**
     * Get full name
     */
    public String getFullName() {
        if (firstName != null && lastName != null) {
            return firstName + " " + lastName;
        } else if (firstName != null) {
            return firstName;
        } else if (lastName != null) {
            return lastName;
        } else {
            return email;
        }
    }
}

/**
 * Base entity với common fields
 */
@MappedSuperclass
@Data
@NoArgsConstructor
@AllArgsConstructor
public abstract class BaseEntity {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;
    
    @UpdateTimestamp
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;
}
```

## 4. Security Component Architecture

### 4.1. JWT Authentication Flow

```java
/**
 * JWT Authentication Filter để validate tokens
 */
@Component
@Slf4j
public class JwtAuthenticationFilter extends OncePerRequestFilter {
    
    private final JwtTokenProvider tokenProvider;
    private final UserDetailsService userDetailsService;
    
    public JwtAuthenticationFilter(JwtTokenProvider tokenProvider, UserDetailsService userDetailsService) {
        this.tokenProvider = tokenProvider;
        this.userDetailsService = userDetailsService;
    }
    
    @Override
    protected void doFilterInternal(
            HttpServletRequest request,
            HttpServletResponse response,
            FilterChain filterChain
    ) throws ServletException, IOException {
        
        try {
            String token = extractTokenFromRequest(request);
            
            if (token != null && tokenProvider.validateToken(token)) {
                String email = tokenProvider.getEmailFromToken(token);
                UserDetails userDetails = userDetailsService.loadUserByUsername(email);
                
                UsernamePasswordAuthenticationToken authentication = 
                    new UsernamePasswordAuthenticationToken(
                        userDetails, null, userDetails.getAuthorities()
                    );
                
                authentication.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
                SecurityContextHolder.getContext().setAuthentication(authentication);
                
                log.debug("Successfully authenticated user: {}", email);
            }
        } catch (Exception e) {
            log.error("Cannot set user authentication: {}", e.getMessage());
            SecurityContextHolder.clearContext();
        }
        
        filterChain.doFilter(request, response);
    }
    
    private String extractTokenFromRequest(HttpServletRequest request) {
        String bearerToken = request.getHeader("Authorization");
        if (bearerToken != null && bearerToken.startsWith("Bearer ")) {
            return bearerToken.substring(7);
        }
        return null;
    }
}

/**
 * JWT Token Provider để generate và validate tokens
 */
@Component
@Slf4j
public class JwtTokenProvider {
    
    @Value("${app.jwt.secret}")
    private String jwtSecret;
    
    @Value("${app.jwt.access-token-expiration}")
    private int accessTokenExpiration;
    
    @Value("${app.jwt.refresh-token-expiration}")
    private int refreshTokenExpiration;
    
    private Key getSigningKey() {
        byte[] keyBytes = Decoders.BASE64.decode(jwtSecret);
        return Keys.hmacShaKeyFor(keyBytes);
    }
    
    /**
     * Generate access token
     */
    public String generateAccessToken(UserDetails userDetails) {
        Map<String, Object> claims = new HashMap<>();
        claims.put("roles", userDetails.getAuthorities().stream()
            .map(GrantedAuthority::getAuthority)
            .collect(Collectors.toList()));
            
        return createToken(claims, userDetails.getUsername(), accessTokenExpiration);
    }
    
    /**
     * Generate refresh token
     */
    public String generateRefreshToken(UserDetails userDetails) {
        return createToken(new HashMap<>(), userDetails.getUsername(), refreshTokenExpiration);
    }
    
    private String createToken(Map<String, Object> claims, String subject, int expiration) {
        Date now = new Date();
        Date expiryDate = new Date(now.getTime() + expiration * 1000L);
        
        return Jwts.builder()
            .setClaims(claims)
            .setSubject(subject)
            .setIssuedAt(now)
            .setExpiration(expiryDate)
            .signWith(getSigningKey(), SignatureAlgorithm.HS512)
            .compact();
    }
    
    /**
     * Validate JWT token
     */
    public boolean validateToken(String token) {
        try {
            Jwts.parserBuilder().setSigningKey(getSigningKey()).build().parseClaimsJws(token);
            return true;
        } catch (SecurityException e) {
            log.error("Invalid JWT signature: {}", e.getMessage());
        } catch (MalformedJwtException e) {
            log.error("Invalid JWT token: {}", e.getMessage());
        } catch (ExpiredJwtException e) {
            log.error("JWT token is expired: {}", e.getMessage());
        } catch (UnsupportedJwtException e) {
            log.error("JWT token is unsupported: {}", e.getMessage());
        } catch (IllegalArgumentException e) {
            log.error("JWT claims string is empty: {}", e.getMessage());
        }
        return false;
    }
    
    /**
     * Get email from JWT token
     */
    public String getEmailFromToken(String token) {
        Claims claims = Jwts.parserBuilder()
            .setSigningKey(getSigningKey())
            .build()
            .parseClaimsJws(token)
            .getBody();
            
        return claims.getSubject();
    }
}
```

## 5. Testing Architecture

### 5.1. Frontend Testing Strategy

```typescript
// Component testing với React Testing Library
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { LoginForm } from '../LoginForm';

/**
 * Test suite cho LoginForm component
 */
describe('LoginForm', () => {
  const mockOnSubmit = jest.fn();
  
  beforeEach(() => {
    mockOnSubmit.mockClear();
  });
  
  it('should render login form correctly', () => {
    render(<LoginForm onSubmit={mockOnSubmit} />);
    
    expect(screen.getByLabelText(/email/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/password/i)).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /login/i })).toBeInTheDocument();
  });
  
  it('should validate required fields', async () => {
    const user = userEvent.setup();
    render(<LoginForm onSubmit={mockOnSubmit} />);
    
    const submitButton = screen.getByRole('button', { name: /login/i });
    await user.click(submitButton);
    
    expect(screen.getByText(/email is required/i)).toBeInTheDocument();
    expect(screen.getByText(/password is required/i)).toBeInTheDocument();
    expect(mockOnSubmit).not.toHaveBeenCalled();
  });
  
  it('should submit form with valid data', async () => {
    const user = userEvent.setup();
    render(<LoginForm onSubmit={mockOnSubmit} />);
    
    await user.type(screen.getByLabelText(/email/i), '<EMAIL>');
    await user.type(screen.getByLabelText(/password/i), 'password123');
    await user.click(screen.getByRole('button', { name: /login/i }));
    
    await waitFor(() => {
      expect(mockOnSubmit).toHaveBeenCalledWith({
        email: '<EMAIL>',
        password: 'password123',
      });
    });
  });
});

// API client testing
import { UserApiClient } from '../UserApiClient';
import { mockAxios } from '../../__mocks__/axios';

describe('UserApiClient', () => {
  let userApiClient: UserApiClient;
  
  beforeEach(() => {
    userApiClient = new UserApiClient('http://localhost:8080');
    mockAxios.reset();
  });
  
  it('should fetch users successfully', async () => {
    const mockUsers = [
      { id: 1, email: '<EMAIL>', firstName: 'User', lastName: 'One' },
      { id: 2, email: '<EMAIL>', firstName: 'User', lastName: 'Two' },
    ];
    
    mockAxios.get.mockResolvedValue({
      data: { success: true, data: mockUsers },
    });
    
    const result = await userApiClient.getUsers();
    
    expect(mockAxios.get).toHaveBeenCalledWith('/api/users', { params: undefined });
    expect(result.data).toEqual(mockUsers);
  });
});
```

### 5.2. Backend Testing Strategy

```java
/**
 * Integration test cho UserController
 */
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
@Testcontainers
class UserControllerIntegrationTest {
    
    @Container
    static PostgreSQLContainer<?> postgres = new PostgreSQLContainer<>("postgres:15")
            .withDatabaseName("testdb")
            .withUsername("test")
            .withPassword("test");
    
    @Autowired
    private TestRestTemplate restTemplate;
    
    @Autowired
    private UserRepository userRepository;
    
    @DynamicPropertySource
    static void configureProperties(DynamicPropertyRegistry registry) {
        registry.add("spring.datasource.url", postgres::getJdbcUrl);
        registry.add("spring.datasource.username", postgres::getUsername);
        registry.add("spring.datasource.password", postgres::getPassword);
    }
    
    @Test
    void shouldCreateUserSuccessfully() {
        // Given
        CreateUserRequest request = CreateUserRequest.builder()
            .email("<EMAIL>")
            .password("password123")
            .firstName("Test")
            .lastName("User")
            .roleIds(Set.of(1L))
            .build();
        
        // When
        ResponseEntity<ApiResponse<UserResponse>> response = restTemplate
            .withBasicAuth("admin", "admin")
            .postForEntity("/api/users", request, new ParameterizedTypeReference<>() {});
        
        // Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.CREATED);
        assertThat(response.getBody().isSuccess()).isTrue();
        assertThat(response.getBody().getData().getEmail()).isEqualTo("<EMAIL>");
        
        // Verify in database
        Optional<User> savedUser = userRepository.findByEmail("<EMAIL>");
        assertThat(savedUser).isPresent();
        assertThat(savedUser.get().getFirstName()).isEqualTo("Test");
    }
}

/**
 * Unit test cho UserService
 */
@ExtendWith(MockitoExtension.class)
class UserServiceTest {
    
    @Mock
    private UserRepository userRepository;
    
    @Mock
    private RoleRepository roleRepository;
    
    @Mock
    private PasswordEncoder passwordEncoder;
    
    @Mock
    private UserMapper userMapper;
    
    @InjectMocks
    private UserService userService;
    
    @Test
    void shouldCreateUserSuccessfully() {
        // Given
        CreateUserRequest request = CreateUserRequest.builder()
            .email("<EMAIL>")
            .password("password123")
            .firstName("Test")
            .lastName("User")
            .roleIds(Set.of(1L))
            .build();
        
        Role userRole = Role.builder().id(1L).name("USER").build();
        User savedUser = User.builder()
            .id(1L)
            .email("<EMAIL>")
            .firstName("Test")
            .lastName("User")
            .roles(Set.of(userRole))
            .build();
        
        when(userRepository.existsByEmail("<EMAIL>")).thenReturn(false);
        when(roleRepository.findAllById(Set.of(1L))).thenReturn(List.of(userRole));
        when(passwordEncoder.encode("password123")).thenReturn("hashedPassword");
        when(userRepository.save(any(User.class))).thenReturn(savedUser);
        
        // When
        User result = userService.createUser(request);
        
        // Then
        assertThat(result.getEmail()).isEqualTo("<EMAIL>");
        assertThat(result.getFirstName()).isEqualTo("Test");
        assertThat(result.getRoles()).contains(userRole);
        
        verify(userRepository).existsByEmail("<EMAIL>");
        verify(roleRepository).findAllById(Set.of(1L));
        verify(passwordEncoder).encode("password123");
        verify(userRepository).save(any(User.class));
    }
    
    @Test
    void shouldThrowExceptionWhenEmailExists() {
        // Given
        CreateUserRequest request = CreateUserRequest.builder()
            .email("<EMAIL>")
            .password("password123")
            .build();
        
        when(userRepository.existsByEmail("<EMAIL>")).thenReturn(true);
        
        // When & Then
        assertThatThrownBy(() -> userService.createUser(request))
            .isInstanceOf(BusinessException.class)
            .hasMessage("Email already exists: <EMAIL>");
        
        verify(userRepository).existsByEmail("<EMAIL>");
        verify(userRepository, never()).save(any(User.class));
    }
}
```

## 6. Performance Optimization Components

### 6.1. Caching Architecture

```java
/**
 * Redis caching configuration
 */
@Configuration
@EnableCaching
public class CacheConfig {
    
    @Bean
    public LettuceConnectionFactory redisConnectionFactory() {
        return new LettuceConnectionFactory(
            new RedisStandaloneConfiguration("localhost", 6379)
        );
    }
    
    @Bean
    public CacheManager cacheManager() {
        RedisCacheManager.Builder builder = RedisCacheManager
            .RedisCacheManagerBuilder
            .fromConnectionFactory(redisConnectionFactory())
            .cacheDefaults(cacheConfiguration(Duration.ofMinutes(10)));
        
        return builder.build();
    }
    
    private RedisCacheConfiguration cacheConfiguration(Duration ttl) {
        return RedisCacheConfiguration.defaultCacheConfig()
            .entryTtl(ttl)
            .serializeKeysWith(RedisSerializationContext.SerializationPair
                .fromSerializer(new StringRedisSerializer()))
            .serializeValuesWith(RedisSerializationContext.SerializationPair
                .fromSerializer(new GenericJackson2JsonRedisSerializer()));
    }
}

/**
 * Service với caching annotations
 */
@Service
public class UserService {
    
    @Cacheable(value = "users", key = "#id")
    public User findUserById(Long id) {
        return userRepository.findById(id)
            .orElseThrow(() -> new ResourceNotFoundException("User not found"));
    }
    
    @CacheEvict(value = "users", key = "#user.id")
    public User updateUser(User user) {
        return userRepository.save(user);
    }
    
    @CacheEvict(value = "users", allEntries = true)
    public User createUser(CreateUserRequest request) {
        // Create user logic
    }
}
```

### 6.2. Database Optimization

```java
/**
 * Custom repository với query optimization
 */
@Repository
public interface UserRepository extends JpaRepository<User, Long> {
    
    @Query("SELECT u FROM User u LEFT JOIN FETCH u.roles WHERE u.id = :id")
    Optional<User> findByIdWithRoles(@Param("id") Long id);
    
    @Query(value = "SELECT * FROM users WHERE active = true ORDER BY created_at DESC LIMIT :limit", 
           nativeQuery = true)
    List<User> findRecentActiveUsers(@Param("limit") int limit);
    
    @Modifying
    @Query("UPDATE User u SET u.active = false WHERE u.id IN :ids")
    void deactivateUsers(@Param("ids") List<Long> ids);
}

/**
 * Database connection pool configuration
 */
@Configuration
public class DatabaseConfig {
    
    @Bean
    @ConfigurationProperties("spring.datasource.hikari")
    public HikariConfig hikariConfig() {
        HikariConfig config = new HikariConfig();
        config.setMaximumPoolSize(20);
        config.setMinimumIdle(5);
        config.setConnectionTimeout(30000);
        config.setIdleTimeout(600000);
        config.setMaxLifetime(1800000);
        config.setLeakDetectionThreshold(60000);
        return config;
    }
}
```

Tài liệu này cung cấp blueprint chi tiết cho việc implement các components trong hệ thống, đảm bảo tính nhất quán và chất lượng code theo các nguyên tắc Clean Architecture và best practices.