# High Level Design (HLD)

## 1. System Overview

### 1.1. Architecture Pattern
Hệ thống sử dụng **3-tier architecture** với clear separation of concerns:
- **Presentation Layer:** NextJS apps (Admin + Web)
- **Business Logic Layer:** Spring Boot REST API
- **Data Access Layer:** PostgreSQL với JPA/Hibernate

### 1.2. Design Principles
- **Single Responsibility Principle:** Mỗi component có responsibility rõ ràng
- **Dependency Injection:** Spring Boot DI container
- **API-First Design:** RESTful APIs với OpenAPI documentation
- **Responsive Design:** Mobile-first approach

## 2. Component Diagrams

### 2.1. System Component Diagram
```mermaid
graph TB
    subgraph "Client Layer"
        AdminApp[Admin App<br/>NextJS + React]
        WebApp[Web App<br/>NextJS + React]
    end
    
    subgraph "Shared Frontend Packages"
        UILib[UI Components<br/>Shadcn/UI]
        ApiClient[API Client<br/>Axios/Fetch]
        AuthLib[Auth Library<br/>JWT Handler]
        StoreLib[Store Library<br/>Zustand]
        TypesLib[Types Library<br/>TypeScript]
        UtilsLib[Utils Library<br/>Common Functions]
    end
    
    subgraph "Backend Services"
        AuthService[Authentication<br/>Service]
        UserService[User Management<br/>Service]
        ContentService[Content Management<br/>Service]
        FileService[File Upload<br/>Service]
    end
    
    subgraph "Data Layer"
        PostgresDB[(PostgreSQL<br/>Database)]
        FileStorage[File Storage<br/>Local/Cloud]
    end
    
    AdminApp --> UILib
    AdminApp --> ApiClient
    AdminApp --> AuthLib
    AdminApp --> StoreLib
    WebApp --> UILib
    WebApp --> ApiClient
    WebApp --> AuthLib
    WebApp --> StoreLib
    
    ApiClient --> AuthService
    ApiClient --> UserService
    ApiClient --> ContentService
    ApiClient --> FileService
    
    AuthService --> PostgresDB
    UserService --> PostgresDB
    ContentService --> PostgresDB
    FileService --> FileStorage
```

### 2.2. Frontend Architecture Detail
```mermaid
graph TB
    subgraph "Admin App Structure"
        AdminPages[Pages<br/>login, dashboard, users]
        AdminComponents[Components<br/>forms, tables, modals]
        AdminHooks[Custom Hooks<br/>useAuth, useApi]
        AdminServices[Services<br/>api calls]
        AdminStores[Stores<br/>user, content state]
    end
    
    subgraph "Web App Structure"
        WebPages[Pages<br/>home, about, contact]
        WebComponents[Components<br/>header, footer, content]
        WebHooks[Custom Hooks<br/>useContent, useApi]
        WebServices[Services<br/>api calls]
        WebStores[Stores<br/>content state]
    end
    
    subgraph "Shared Packages"
        SharedUI[UI Package<br/>Button, Input, Modal]
        SharedTypes[Types Package<br/>User, Content interfaces]
        SharedUtils[Utils Package<br/>formatters, validators]
        SharedAuth[Auth Package<br/>token management]
        SharedAPI[API Package<br/>client configuration]
        SharedStore[Store Package<br/>base stores]
    end
    
    AdminComponents --> SharedUI
    WebComponents --> SharedUI
    AdminHooks --> SharedAuth
    WebHooks --> SharedAuth
    AdminServices --> SharedAPI
    WebServices --> SharedAPI
    AdminStores --> SharedStore
    WebStores --> SharedStore
```

### 2.3. Backend Architecture Detail
```mermaid
graph TB
    subgraph "Controller Layer"
        AuthController[AuthController<br/>login, register, refresh]
        UserController[UserController<br/>CRUD operations]
        ContentController[ContentController<br/>content management]
        FileController[FileController<br/>upload, download]
    end
    
    subgraph "Service Layer"
        AuthService[AuthService<br/>authentication logic]
        UserService[UserService<br/>user business logic]
        ContentService[ContentService<br/>content business logic]
        FileService[FileService<br/>file handling logic]
    end
    
    subgraph "Repository Layer"
        UserRepository[UserRepository<br/>JPA repository]
        ContentRepository[ContentRepository<br/>JPA repository]
        RoleRepository[RoleRepository<br/>JPA repository]
    end
    
    subgraph "Entity Layer"
        UserEntity[User Entity<br/>JPA entity]
        ContentEntity[Content Entity<br/>JPA entity]
        RoleEntity[Role Entity<br/>JPA entity]
    end
    
    AuthController --> AuthService
    UserController --> UserService
    ContentController --> ContentService
    FileController --> FileService
    
    AuthService --> UserRepository
    UserService --> UserRepository
    ContentService --> ContentRepository
    AuthService --> RoleRepository
    
    UserRepository --> UserEntity
    ContentRepository --> ContentEntity
    RoleRepository --> RoleEntity
```

## 3. API Design

### 3.1. REST API Endpoints

#### Authentication APIs
```
POST /api/auth/login
POST /api/auth/register
POST /api/auth/refresh
POST /api/auth/logout
GET  /api/auth/me
```

#### User Management APIs
```
GET    /api/users              # List users (admin only)
GET    /api/users/{id}         # Get user by ID
POST   /api/users              # Create user (admin only)
PUT    /api/users/{id}         # Update user
DELETE /api/users/{id}         # Delete user (admin only)
```

#### Content Management APIs
```
GET    /api/content            # List content (public/admin)
GET    /api/content/{id}       # Get content by ID
POST   /api/content            # Create content (admin only)
PUT    /api/content/{id}       # Update content (admin only)
DELETE /api/content/{id}       # Delete content (admin only)
```

#### File Management APIs
```
POST   /api/files/upload       # Upload file
GET    /api/files/{id}         # Download file
DELETE /api/files/{id}         # Delete file (admin only)
```

### 3.2. API Request/Response Format

#### Standard Response Format
```json
{
  "success": true,
  "data": {},
  "message": "Success message",
  "errors": [],
  "timestamp": "2024-01-01T00:00:00Z"
}
```

#### Error Response Format
```json
{
  "success": false,
  "data": null,
  "message": "Error occurred",
  "errors": [
    {
      "field": "email",
      "message": "Email is required"
    }
  ],
  "timestamp": "2024-01-01T00:00:00Z"
}
```

#### Authentication Request/Response
```json
// Login Request
{
  "email": "<EMAIL>",
  "password": "password123"
}

// Login Response
{
  "success": true,
  "data": {
    "accessToken": "jwt-access-token",
    "refreshToken": "jwt-refresh-token",
    "user": {
      "id": 1,
      "email": "<EMAIL>",
      "firstName": "John",
      "lastName": "Doe",
      "roles": ["USER"]
    }
  }
}
```

### 3.3. API Security Design

#### Authentication Flow
```mermaid
sequenceDiagram
    participant C as Client
    participant A as Auth Service
    participant D as Database
    
    C->>A: POST /api/auth/login
    A->>D: Validate credentials
    D-->>A: User data
    A->>A: Generate JWT tokens
    A-->>C: Access + Refresh tokens
    
    Note over C,A: Subsequent requests
    C->>A: API request + Bearer token
    A->>A: Validate JWT token
    A-->>C: API response
    
    Note over C,A: Token refresh
    C->>A: POST /api/auth/refresh
    A->>A: Validate refresh token
    A-->>C: New access token
```

#### Authorization Levels
- **Public:** No authentication required
- **User:** Valid access token required
- **Admin:** Admin role required
- **Owner:** Resource owner or admin

## 4. Database Schema Design

### 4.1. Entity Relationship Diagram
```mermaid
erDiagram
    USER {
        bigint id PK
        varchar email UK
        varchar password_hash
        varchar first_name
        varchar last_name
        boolean active
        timestamp created_at
        timestamp updated_at
    }
    
    ROLE {
        bigint id PK
        varchar name UK
        varchar description
    }
    
    USER_ROLE {
        bigint user_id FK
        bigint role_id FK
    }
    
    CONTENT {
        bigint id PK
        varchar title
        text content
        varchar status
        bigint author_id FK
        timestamp published_at
        timestamp created_at
        timestamp updated_at
    }
    
    FILE {
        bigint id PK
        varchar original_name
        varchar file_name
        varchar file_path
        varchar mime_type
        bigint file_size
        bigint uploaded_by FK
        timestamp created_at
    }
    
    USER ||--o{ USER_ROLE : has
    ROLE ||--o{ USER_ROLE : assigned_to
    USER ||--o{ CONTENT : authors
    USER ||--o{ FILE : uploads
```

### 4.2. Database Tables Detail

#### Users Table
```sql
CREATE TABLE users (
    id BIGSERIAL PRIMARY KEY,
    email VARCHAR(255) NOT NULL UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    first_name VARCHAR(100),
    last_name VARCHAR(100),
    active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### Roles Table
```sql
CREATE TABLE roles (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(50) NOT NULL UNIQUE,
    description VARCHAR(255)
);

INSERT INTO roles (name, description) VALUES 
('ADMIN', 'Administrator with full access'),
('USER', 'Regular user with limited access');
```

#### Content Table
```sql
CREATE TABLE content (
    id BIGSERIAL PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    content TEXT,
    status VARCHAR(20) DEFAULT 'DRAFT',
    author_id BIGINT REFERENCES users(id),
    published_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 4.3. Database Indexes
```sql
-- Performance indexes
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_content_author ON content(author_id);
CREATE INDEX idx_content_status ON content(status);
CREATE INDEX idx_content_published ON content(published_at);
CREATE INDEX idx_files_uploaded_by ON files(uploaded_by);
```

## 5. Frontend Component Design

### 5.1. UI Component Library Structure
```
packages/ui/src/components/
├── atoms/                  # Basic components
│   ├── Button/
│   ├── Input/
│   ├── Label/
│   └── Avatar/
├── molecules/              # Compound components
│   ├── FormField/
│   ├── SearchBox/
│   └── UserCard/
├── organisms/              # Complex components
│   ├── Header/
│   ├── Sidebar/
│   ├── DataTable/
│   └── ContentEditor/
└── templates/              # Layout components
    ├── DashboardLayout/
    ├── AuthLayout/
    └── PublicLayout/
```

### 5.2. State Management Design

#### Zustand Store Structure
```typescript
// User Store
interface UserStore {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (credentials: LoginCredentials) => Promise<void>;
  logout: () => void;
  updateProfile: (data: UpdateProfileData) => Promise<void>;
}

// Content Store
interface ContentStore {
  contents: Content[];
  currentContent: Content | null;
  isLoading: boolean;
  fetchContents: () => Promise<void>;
  createContent: (data: CreateContentData) => Promise<void>;
  updateContent: (id: number, data: UpdateContentData) => Promise<void>;
  deleteContent: (id: number) => Promise<void>;
}
```

### 5.3. Routing Design

#### Admin App Routes
```
/admin/
├── /auth/
│   ├── /login              # Login page
│   └── /register           # Register page (admin only)
├── /dashboard              # Dashboard home
├── /users/                 # User management
│   ├── /                   # Users list
│   ├── /create             # Create user
│   └── /[id]/edit          # Edit user
├── /content/               # Content management
│   ├── /                   # Content list
│   ├── /create             # Create content
│   └── /[id]/edit          # Edit content
└── /settings/              # System settings
```

#### Web App Routes
```
/
├── /                       # Home page
├── /about/                 # About page
├── /contact/               # Contact page
├── /blog/                  # Blog listing
│   └── /[slug]/            # Blog post detail
└── /search/                # Search results
```

## 6. Security Design

### 6.1. Authentication Security
- **Password Hashing:** BCrypt với salt rounds >= 12
- **JWT Security:** RS256 algorithm, short-lived access tokens (15 min)
- **Refresh Tokens:** Long-lived (7 days), stored securely, rotation on use
- **Session Management:** Stateless JWT-based

### 6.2. Authorization Matrix
| Resource | Public | User | Admin |
|----------|--------|------|-------|
| GET /api/content | ✅ | ✅ | ✅ |
| POST /api/content | ❌ | ❌ | ✅ |
| PUT /api/content | ❌ | Owner only | ✅ |
| DELETE /api/content | ❌ | ❌ | ✅ |
| GET /api/users | ❌ | Own profile | ✅ |
| POST /api/users | ❌ | ❌ | ✅ |

### 6.3. Input Validation
- **Frontend:** Zod schema validation
- **Backend:** Bean Validation (JSR-303) annotations
- **Sanitization:** XSS protection, SQL injection prevention
- **File Upload:** File type restriction, size limits, virus scanning

### 6.4. Security Headers
```
Content-Security-Policy: default-src 'self'
X-Frame-Options: DENY
X-Content-Type-Options: nosniff
Strict-Transport-Security: max-age=31536000; includeSubDomains
X-XSS-Protection: 1; mode=block
```

## 7. Performance Design

### 7.1. Frontend Performance
- **Code Splitting:** Dynamic imports cho route-based splitting
- **Bundle Optimization:** Tree shaking, minification
- **Image Optimization:** NextJS Image component với lazy loading
- **Caching:** Browser caching, service worker caching

### 7.2. Backend Performance
- **Database Optimization:** Connection pooling, query optimization
- **Caching Strategy:** Redis cho session và API caching
- **API Response:** Pagination, field selection, compression
- **Static Assets:** CDN delivery, gzip compression

### 7.3. Database Performance
```sql
-- Query optimization examples
EXPLAIN ANALYZE SELECT * FROM content 
WHERE status = 'PUBLISHED' 
ORDER BY published_at DESC 
LIMIT 20;

-- Connection pooling configuration
spring.datasource.hikari.maximum-pool-size=20
spring.datasource.hikari.minimum-idle=5
```

## 8. Error Handling Design

### 8.1. Frontend Error Handling
```typescript
// Error Boundary Component
class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null };
  }
  
  static getDerivedStateFromError(error) {
    return { hasError: true, error };
  }
  
  componentDidCatch(error, errorInfo) {
    // Log error to monitoring service
    console.error('Error caught by boundary:', error, errorInfo);
  }
}

// API Error Handling
const handleApiError = (error: AxiosError) => {
  if (error.response?.status === 401) {
    // Redirect to login
    router.push('/auth/login');
  } else if (error.response?.status >= 500) {
    // Show generic error message
    toast.error('Server error occurred');
  }
};
```

### 8.2. Backend Error Handling
```java
@ControllerAdvice
public class GlobalExceptionHandler {
    
    @ExceptionHandler(ValidationException.class)
    public ResponseEntity<ErrorResponse> handleValidation(ValidationException e) {
        return ResponseEntity.badRequest()
            .body(ErrorResponse.builder()
                .success(false)
                .message("Validation failed")
                .errors(e.getErrors())
                .build());
    }
    
    @ExceptionHandler(EntityNotFoundException.class)
    public ResponseEntity<ErrorResponse> handleNotFound(EntityNotFoundException e) {
        return ResponseEntity.notFound()
            .build();
    }
}
```

## 9. Deployment Design

### 9.1. Container Architecture
```dockerfile
# Frontend Dockerfile
FROM node:20-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
RUN npm run build
EXPOSE 3000
CMD ["npm", "start"]

# Backend Dockerfile
FROM openjdk:21-jdk-slim
WORKDIR /app
COPY target/*.jar app.jar
EXPOSE 8080
CMD ["java", "-jar", "app.jar"]
```

### 9.2. Docker Compose Setup
```yaml
version: '3.8'
services:
  frontend:
    build: ./src/frontend
    ports:
      - "3000:3000"
    environment:
      - NEXT_PUBLIC_API_URL=http://backend:8080
  
  backend:
    build: ./src/backend
    ports:
      - "8080:8080"
    environment:
      - SPRING_PROFILES_ACTIVE=production
      - DATABASE_URL=**************************************/myapp
    depends_on:
      - db
  
  db:
    image: postgres:15
    environment:
      - POSTGRES_DB=myapp
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data

volumes:
  postgres_data:
```

### 9.3. CI/CD Pipeline Design
```yaml
# .github/workflows/deploy.yml
name: Deploy Application
on:
  push:
    branches: [main, develop]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '20'
      - name: Install dependencies
        run: |
          cd src/frontend && pnpm install
      - name: Run tests
        run: |
          cd src/frontend && pnpm test
      - name: Setup Java
        uses: actions/setup-java@v3
        with:
          java-version: '21'
      - name: Run backend tests
        run: |
          cd src/backend && mvn test

  deploy:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    steps:
      - name: Deploy to production
        run: |
          # Deployment script
```

## 10. Monitoring và Logging Design

### 10.1. Application Monitoring
```yaml
# Prometheus metrics configuration
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  metrics:
    export:
      prometheus:
        enabled: true
```

### 10.2. Logging Strategy
```java
// Structured logging with SLF4J
@Slf4j
@RestController
public class UserController {
    
    @GetMapping("/api/users/{id}")
    public ResponseEntity<User> getUser(@PathVariable Long id) {
        log.info("Fetching user with id: {}", id);
        
        try {
            User user = userService.findById(id);
            log.info("Successfully fetched user: {}", user.getEmail());
            return ResponseEntity.ok(user);
        } catch (Exception e) {
            log.error("Error fetching user with id: {}", id, e);
            throw e;
        }
    }
}
```

### 10.3. Health Checks
```java
@Component
public class DatabaseHealthIndicator implements HealthIndicator {
    
    @Override
    public Health health() {
        try {
            // Check database connectivity
            return Health.up()
                .withDetail("database", "Available")
                .build();
        } catch (Exception e) {
            return Health.down()
                .withDetail("database", "Unavailable")
                .withException(e)
                .build();
        }
    }
}
```