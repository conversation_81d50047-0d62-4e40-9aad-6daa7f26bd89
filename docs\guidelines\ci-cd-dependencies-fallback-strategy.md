# Hướng dẫn CI/CD: Smart Workflow Optimization và Dependencies Management

## Tổng quan

Hướng dẫn này mô tả bốn cải tiến quan trọng trong CI/CD pipeline:
1. Logic fallback để đảm bảo việc cài đặt dependencies luôn thành công
2. Smart CI build logic với target branch và label control
3. Bỏ qua CI build cho Draft Pull Requests để tiết kiệm tài nguyên
4. Tr<PERSON>h duplicate CI runs để tối ưu hóa workflow

## Vấn đề

### Dependencies Issues
Trong quá trình CI/CD, việc cài dependencies có thể thất bại do:
- File `pnpm-lock.yaml` bị corrupt hoặc không tương thích
- Các dependency trong lock file không khả dụng
- Version conflicts giữa lock file và package.json

### Tài nguyên CI/CD lãng phí
- CI build chạy cho Draft PRs khi code chưa sẵn sàng
- CI build chạy cho tất cả PRs không phân biệt importance
- Duplicate CI runs khi push và PR events trigger cùng lúc
- Gây tốn tài nguyên và làm chậm pipeline cho các PR quan trọng

## Giải pháp

### Chiến lược Fallback

```yaml
- name: Install Frontend Dependencies
  run: |
    echo "📦 Installing Frontend dependencies..."
    cd src/frontend
    
    # Kiểm tra và thử cài dependencies với pnpm-lock.yaml
    if [ -f "pnpm-lock.yaml" ]; then
      echo "🔍 Tìm thấy pnpm-lock.yaml, thử cài dependencies với --frozen-lockfile..."
      if pnpm install --frozen-lockfile; then
        echo "✅ Cài dependencies thành công với pnpm-lock.yaml!"
      else
        echo "⚠️ Không thể cài dependencies với pnpm-lock.yaml, chuyển sang fallback..."
        echo "🔄 Cài dependencies theo cách thông thường..."
        pnpm install
        echo "✅ Cài dependencies thành công với fallback!"
      fi
    else
      echo "⚠️ Không tìm thấy pnpm-lock.yaml, cài dependencies theo cách thông thường..."
      pnpm install
      echo "✅ Dependencies được cài đặt thành công!"
    fi
```

### Logic Flow

1. **Kiểm tra file pnpm-lock.yaml**
   - Nếu có: thử cài với `--frozen-lockfile`
   - Nếu không có: chuyển thẳng sang fallback

2. **Xử lý thành công**
   - Log thông báo thành công
   - Tiếp tục build process

3. **Xử lý thất bại**
   - Log warning về việc fallback
   - Chạy `pnpm install` không có flag
   - Log thông báo thành công của fallback

## Chiến lược Draft PR Handling

### Workflow Trigger Configuration

```yaml
on:
  push:
    branches: [ main, develop, feature/* ]
  pull_request:
    branches: [ main, develop ]
    types: [ opened, synchronize, reopened, ready_for_review ]
```

### Logic kiểm tra điều kiện

```yaml
check-conditions:
  name: Check Build Conditions
  runs-on: ubuntu-latest
  outputs:
    should-build: ${{ steps.check.outputs.should-build }}
  steps:
    - name: Check if should build
      id: check
      run: |
        echo "🔍 Kiểm tra điều kiện build..."
        
        # Nếu là push event, luôn build
        if [[ "${{ github.event_name }}" == "push" ]]; then
          echo "📤 Push event detected - sẽ chạy build"
          echo "should-build=true" >> $GITHUB_OUTPUT
          exit 0
        fi
        
        # Nếu là PR event, kiểm tra draft status
        if [[ "${{ github.event_name }}" == "pull_request" ]]; then
          if [[ "${{ github.event.pull_request.draft }}" == "true" ]]; then
            echo "📝 Draft PR detected - bỏ qua build"
            echo "should-build=false" >> $GITHUB_OUTPUT
          else
            echo "✅ Ready PR detected - sẽ chạy build"
            echo "should-build=true" >> $GITHUB_OUTPUT
          fi
          exit 0
        fi
        
        # Default: build
        echo "should-build=true" >> $GITHUB_OUTPUT
```

### Job dependencies

```yaml
build-backend:
  name: Build Backend
  runs-on: ubuntu-latest
  needs: check-conditions
  if: needs.check-conditions.outputs.should-build == 'true'
```

## Chiến lược Smart CI Build Logic

### Workflow Trigger với Label Support

```yaml
on:
  push:
    branches: [ main, develop ]
  pull_request:
    types: [ opened, synchronize, reopened, ready_for_review, labeled, unlabeled ]
```

### Logic Decision Matrix

| Scenario       | Target Branch | Draft | Label `ci-build` | Build?           |
| -------------- | ------------- | ----- | ---------------- | ---------------- |
| PR → main      | main          | No    | -                | ✅ Bắt buộc       |
| PR → develop   | develop       | No    | -                | ✅ Bắt buộc       |
| PR → feature/* | feature/*     | No    | Yes              | ✅ Manual trigger |
| PR → feature/* | feature/*     | No    | No               | ❌ Skip           |
| Any PR         | any           | Yes   | -                | ❌ Skip draft     |

### Enhanced Logic Implementation

```bash
# Kiểm tra target branch
TARGET_BRANCH="${{ github.event.pull_request.base.ref }}"

if [[ "$TARGET_BRANCH" == "main" || "$TARGET_BRANCH" == "develop" ]]; then
  echo "✅ Protected branch - bắt buộc build"
  echo "should-build=true" >> $GITHUB_OUTPUT
else
  # Kiểm tra label ci-build
  LABELS='${{ toJson(github.event.pull_request.labels.*.name) }}'
  if echo "$LABELS" | grep -q "ci-build"; then
    echo "🏷️ Label 'ci-build' found - build"
    echo "should-build=true" >> $GITHUB_OUTPUT
  else
    echo "⏭️ No ci-build label - skip"
    echo "should-build=false" >> $GITHUB_OUTPUT
  fi
fi
```

**Lợi ích:**
- **Resource efficiency**: Chỉ build khi cần thiết
- **Manual control**: Developer control bằng label
- **Protected branches**: Main/develop luôn được test

## Chiến lược Tránh Duplicate CI Runs

### Vấn đề Duplicate Events

Khi có PR từ feature branch, cả push event và pull_request event đều trigger:

**Problematic Configuration:**
```yaml
on:
  push:
    branches: [ main, develop, feature/* ]  # ← Bao gồm feature/*
  pull_request:
    branches: [ main, develop ]
```

**Kết quả:** Feature branch push → 2 CI runs (push + PR events)

### Giải pháp Optimized Triggers

```yaml
on:
  push:
    branches: [ main, develop ]             # ← Chỉ protected branches
  pull_request:
    branches: [ main, develop ]
    types: [ opened, synchronize, reopened, ready_for_review ]
```

### Enhanced Concurrency Control

```yaml
concurrency:
  group: ci-build-${{ github.event_name }}-${{ github.ref }}
  cancel-in-progress: true
```

**Lợi ích:**
- Phân biệt push và PR events trong concurrency group
- Tự động cancel các runs đang chờ khi có update mới
- Giảm 50% số lượng CI runs

## Lợi ích

### Dependencies Fallback Strategy
- **Tính ổn định**: CI/CD pipeline ít bị fail do dependency issues
- **Tự động recovery**: Tự động recovery khi lock file có vấn đề
- **Tính minh bạch**: Clear logging cho mỗi step, dễ debug khi có issue
- **Tính linh hoạt**: Hoạt động với hoặc không có lock file

### Smart CI Build Logic
- **Resource efficiency**: Chỉ build khi thực sự cần thiết
- **Manual control**: Developer có thể trigger bằng label `ci-build`
- **Protected branches**: Main/develop luôn được test đầy đủ
- **Flexible workflow**: Experimental PRs có thể skip hoặc manual test

### Draft PR Handling
- **Tiết kiệm tài nguyên**: Không chạy CI cho code chưa sẵn sàng
- **Tối ưu hóa pipeline**: Ưu tiên tài nguyên cho các PR quan trọng
- **Trải nghiệm dev tốt**: Developer có thể tạo draft PR mà không lo CI fail
- **Linh hoạt workflow**: Tự động kích hoạt khi chuyển từ draft sang ready

### Duplicate CI Prevention
- **Resource efficiency**: Giảm 50% số lượng CI runs
- **Faster feedback**: Tăng tốc pipeline do ít queue
- **Clear separation**: Push events cho protected branches, PR events cho features
- **Smart concurrency**: Intelligent cancellation và resource management

## Best Practices

### Logging
- Sử dụng emoji và màu sắc để dễ đọc logs
- Mô tả rõ ràng từng bước đang thực hiện
- Phân biệt giữa success, warning và error

### Error Handling
- Luôn có fallback strategy
- Không để CI fail vì dependency issues
- Log đầy đủ thông tin để debug

### Performance
- Cache pnpm store để tăng tốc
- Sử dụng frozen-lockfile khi có thể để đảm bảo consistency

## Áp dụng cho các Package Manager khác

### NPM
```bash
if [ -f "package-lock.json" ]; then
  if npm ci; then
    echo "✅ Installed with npm ci"
  else
    npm install
  fi
else
  npm install
fi
```

### Yarn
```bash
if [ -f "yarn.lock" ]; then
  if yarn install --frozen-lockfile; then
    echo "✅ Installed with frozen lockfile"
  else
    yarn install
  fi
else
  yarn install
fi
```

## Tags
`ci-cd`, `dependencies`, `fallback`, `pnpm`, `frontend`, `build-optimization`, `draft-pr`, `pull-request`, `workflow-optimization`, `duplicate-prevention`, `resource-management`, `smart-logic`, `label-control`, `target-branch`
