# Path-Based Selective CI Building Enhancement

## Vấn đề
CI workflow hiện tại đã có smart logic cho target branch và label control, nhưng vẫn thiếu **path-based selective building**. Điều này dẫn đến:

- Build cả frontend + backend khi chỉ 1 phần thay đổi
- Lãng phí tài nguyên và thời gian CI
- Khó debug khi lỗi chỉ ở 1 component
- Developer experience chưa tối ưu

## Phân tích chi tiết

### Logic hiện tại:
```yaml
build-backend:
  if: needs.check-conditions.outputs.should-build == 'true'
  
build-frontend:  
  if: needs.check-conditions.outputs.should-build == 'true'
```

### Vấn đề:
- Không phân biệt changes ở `src/frontend/` vs `src/backend/`
- Build 2 jobs ngay cả khi chỉ sửa 1 file frontend
- Không tận dụng được monorepo structure

## Giải pháp

### 1. Thêm job detect changes
```yaml
detect-changes:
  name: Detect File Changes
  runs-on: ubuntu-latest
  needs: check-conditions
  if: needs.check-conditions.outputs.should-build == 'true'
  outputs:
    backend-changed: ${{ steps.changes.outputs.backend }}
    frontend-changed: ${{ steps.changes.outputs.frontend }}
    docs-changed: ${{ steps.changes.outputs.docs }}
    ci-changed: ${{ steps.changes.outputs.ci }}
  steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 2
    
    - name: Detect file changes
      uses: dorny/paths-filter@v2
      id: changes
      with:
        filters: |
          backend:
            - 'src/backend/**'
            - 'docs/backend/**'
          frontend:
            - 'src/frontend/**'
            - 'docs/frontend/**'
          docs:
            - 'docs/requirements/**'
            - 'docs/architecture/**'
            - 'docs/guidelines/**'
          ci:
            - '.github/workflows/**'
```

### 2. Enhanced build conditions
```yaml
build-backend:
  needs: [check-conditions, detect-changes]
  if: |
    needs.check-conditions.outputs.should-build == 'true' && 
    (needs.detect-changes.outputs.backend-changed == 'true' || 
     needs.detect-changes.outputs.ci-changed == 'true' ||
     needs.detect-changes.outputs.docs-changed == 'true')

build-frontend:
  needs: [check-conditions, detect-changes]  
  if: |
    needs.check-conditions.outputs.should-build == 'true' && 
    (needs.detect-changes.outputs.frontend-changed == 'true' || 
     needs.detect-changes.outputs.ci-changed == 'true' ||
     needs.detect-changes.outputs.docs-changed == 'true')
```

### 3. Smart status reporting
```yaml
build-status:
  needs: [check-conditions, detect-changes, build-backend, build-frontend]
  if: always() && needs.check-conditions.outputs.should-build == 'true'
  steps:
    - name: Smart Build Summary
      run: |
        echo "📊 Path-Based Build Results:"
        echo ""
        echo "🔍 File Changes Detection:"
        echo "  Backend: ${{ needs.detect-changes.outputs.backend-changed }}"
        echo "  Frontend: ${{ needs.detect-changes.outputs.frontend-changed }}"
        echo "  Docs: ${{ needs.detect-changes.outputs.docs-changed }}"
        echo "  CI: ${{ needs.detect-changes.outputs.ci-changed }}"
        echo ""
        
        # Backend status
        if [[ "${{ needs.detect-changes.outputs.backend-changed }}" == "true" || "${{ needs.detect-changes.outputs.ci-changed }}" == "true" || "${{ needs.detect-changes.outputs.docs-changed }}" == "true" ]]; then
          echo "🔨 Backend Build: ${{ needs.build-backend.result }}"
        else
          echo "⏭️ Backend Build: Skipped (no relevant changes)"
        fi
        
        # Frontend status  
        if [[ "${{ needs.detect-changes.outputs.frontend-changed }}" == "true" || "${{ needs.detect-changes.outputs.ci-changed }}" == "true" || "${{ needs.detect-changes.outputs.docs-changed }}" == "true" ]]; then
          echo "🔨 Frontend Build: ${{ needs.build-frontend.result }}"
        else
          echo "⏭️ Frontend Build: Skipped (no relevant changes)"
        fi
        
        echo ""
        echo "💡 Path-based selective building saves time and resources!"
```

## Test Scenarios

### Scenario 1: Chỉ sửa frontend
```bash
# Files changed: src/frontend/apps/admin/page.tsx
# Expected: 
#   ✅ Frontend build runs
#   ⏭️ Backend build skipped
```

### Scenario 2: Chỉ sửa backend  
```bash
# Files changed: src/backend/src/main/java/Service.java
# Expected:
#   ⏭️ Frontend build skipped  
#   ✅ Backend build runs
```

### Scenario 3: Sửa CI workflow
```bash
# Files changed: .github/workflows/ci-build.yml
# Expected:
#   ✅ Both builds run (CI changes affect all)
```

### Scenario 4: Sửa documentation
```bash
# Files changed: docs/requirements/cms_product_backlog.md
# Expected:
#   ✅ Both builds run (shared docs affect all)
```

### Scenario 5: Sửa cả frontend + backend
```bash
# Files changed: 
#   - src/frontend/apps/web/layout.tsx
#   - src/backend/src/main/java/Controller.java
# Expected:
#   ✅ Both builds run
```

## Lợi ích

### Resource Optimization
- **50-70% CI time reduction** khi chỉ sửa 1 component
- **Selective building** based on actual changes
- **Smart dependency detection** (docs/CI affect both)

### Developer Experience
- **Faster feedback** cho single-component changes
- **Clear build reasons** in status summary
- **Predictable behavior** với path-based rules

### Maintainability
- **Modular CI** dễ extend và maintain
- **Clear separation** giữa frontend/backend concerns
- **Flexible rules** có thể adjust paths dễ dàng

## Implementation Steps

1. **Add dependency**: `dorny/paths-filter@v2` action
2. **Create job**: `detect-changes` với path filters
3. **Update conditions**: cho `build-backend` và `build-frontend` 
4. **Enhance reporting**: trong `build-status` job
5. **Test thoroughly**: với các scenarios khác nhau
6. **Document**: update team guidelines

## Tags
`ci-cd`, `path-based-building`, `selective-ci`, `monorepo`, `optimization`, `dorny-paths-filter`
