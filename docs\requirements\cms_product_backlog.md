---

# **Product Backlog – <PERSON><PERSON> thống CMS**

*Phiê<PERSON> bản*: 1.1 – *Ngày*: 29/07/2025\
*Người biên soạn*: OSP BA Assistant

---

## 1. Tổng quan dự án

Hệ thống **Content Management System (CMS)** hỗ trợ biên tập, phê duyệt và phân phối nội dung đa kênh (website, mobile app, mạng xã hội…) một cách linh hoạt, b<PERSON><PERSON> mậ<PERSON>, chuẩn SEO và tối ưu trải nghiệm người đọc.

**Mục tiêu kinh doanh chính**

- Tăng tốc độ xuất bản tin bài ≥ **30%** so với quy trình thủ công hiện tại.
- Đả<PERSON> bảo **100%** nội dung xuất bản tuân thủ quy trình phê duyệt 2 bước.
- R<PERSON>t ngắn thời gian tì<PERSON> kiếm nội dung & media xuống < **3 giây**.

---

## 2. <PERSON><PERSON> s<PERSON>ch <PERSON>

| Mã Epic     | Epic                              | Mục tiêu                                                      | Gi<PERSON> tr<PERSON> chính                                                 |
| ----------- | --------------------------------- | ------------------------------------------------------------- | ------------------------------------------------------------- |
| **CMS‑EP1** | Quản lý Kênh & Chuyên mục         | Tạo, cấu trúc, hiển thị kênh tin và chuyên mục dạng phân cấp. | Dễ dàng tổ chức nội dung, hỗ trợ SEO, mở rộng kênh phân phối. |
| **CMS‑EP2** | Quản lý Tin bài                   | Soạn thảo, chỉnh sửa, gắn thẻ, lập lịch đăng tin.             | Nâng cao hiệu suất biên tập & chất lượng nội dung.            |
| **CMS‑EP3** | Phê duyệt & Xuất bản              | Quy trình 2 bước, nhật ký, thông báo, khôi phục.              | Đảm bảo kiểm soát chất lượng & tuân thủ.                      |
| **CMS‑EP4** | Thư viện Media                    | Kho lưu trữ, batch upload, bộ sưu tập.                        | Quản lý tài sản số tập trung và tái sử dụng dễ dàng.          |
| **CMS‑EP5** | Người dùng & Phân quyền           | Vai trò, quyền hạn, nhật ký hoạt động.                        | Bảo mật truy cập và trách nhiệm rõ ràng.                      |
| **CMS‑EP6** | Tìm kiếm & Lọc                    | Tìm kiếm toàn cục, lọc nâng cao.                              | Tiết kiệm thời gian tra cứu, cải thiện trải nghiệm.           |
| **CMS‑EP7** | Hiển thị & SEO                    | Bài nổi bật, meta tự động, preview.                           | Tăng lưu lượng truy cập & tối ưu hiển thị trên đa thiết bị.   |
| **CMS‑EP8** | Giao diện Trang chủ/Kênh/Chi tiết | Layout cố định, cấu hình nội dung động.                       | Thống nhất thương hiệu, triển khai nhanh tính năng frontend.  |

---

## 3. Product Backlog chi tiết

**Ký hiệu**

- **AC**: *Acceptance Criteria* (định dạng Gherkin).
- **DoD**: *Definition of Done* (check‑list kiểm chứng kỹ thuật & nghiệp vụ).

> *Mẹo*: Tất cả checklist DoD đều phải **ticked** ✅ trước khi story được đóng.

### 3.1 CMS‑EP1 – Quản lý Kênh & Chuyên mục

| ID              | User Story                                                                                       | Ưu tiên | Điểm |
| --------------- | ------------------------------------------------------------------------------------------------ | ------- | ---- |
| **CMS‑EP1‑US1** | *Là* **Admin**, tôi muốn **tạo mới kênh tin** để phân phối nội dung sang các nền tảng khác nhau. | H       | 5    |

**AC**

```
Scenario: Tạo kênh thành công
  Given tôi ở trang “Quản lý Kênh”
  When tôi nhập "Tên kênh" hợp lệ và nhấn "Lưu"
  Then hệ thống tạo kênh với trạng thái "Đang hoạt động" và hiển thị trong danh sách

Scenario: Tên kênh trùng lặp
  Given đã tồn tại kênh "Thời sự"
  When tôi nhập "Thời sự" và nhấn "Lưu"
  Then hệ thống báo lỗi "Tên kênh đã tồn tại"
```

**DoD**

-

\| **CMS‑EP1‑US2** | *Là* **Admin**, tôi muốn **sửa/xóa kênh tin** để giữ cấu trúc kênh luôn chính xác. | H | 3 |

**AC**

```
Scenario: Cập nhật tên & mô tả
  Given tôi ở form “Chỉnh sửa Kênh”
  When tôi sửa mô tả và nhấn "Cập nhật"
  Then dữ liệu kênh được lưu và hiển thị phiên bản mới trên danh sách

Scenario: Xóa kênh không chứa chuyên mục
  Given kênh "Review sách" không có chuyên mục con
  When tôi nhấn "Xóa"
  Then hệ thống yêu cầu xác nhận và kênh bị xóa vĩnh viễn

Scenario: Xóa kênh có chuyên mục
  Given kênh "Giải trí" có 2 chuyên mục con
  When tôi nhấn "Xóa"
  Then hệ thống thông báo "Không thể xóa kênh có chuyên mục con"
```

**DoD**

-

\| **CMS‑EP1‑US3** | *Là* **Editor**, tôi muốn **tạo chuyên mục cha/con** để tổ chức bài viết theo phân cấp. | H | 5 |

**AC**

```
Scenario: Tạo chuyên mục cấp 2
  Given tôi chọn kênh "Công nghệ"
  When tôi nhập "Mobile" và chọn "Công nghệ" làm cha
  Then chuyên mục "Mobile" xuất hiện dưới dạng con của "Công nghệ"

Scenario: Hiển thị breadcrumb
  Given chuyên mục "Apple" (cấp 3)
  When tôi mở trang danh sách chuyên mục
  Then breadcrumb hiển thị "Công nghệ / Mobile / Apple"
```

**DoD**

-

\| **CMS‑EP1‑US4** | *Là* **SEO Specialist**, tôi muốn **cấu hình mô tả, ảnh đại diện, slug & meta** cho chuyên mục để tối ưu SEO. | M | 5 |

**AC**

```
Scenario: Lưu meta thành công
  Given tôi ở form “Chỉnh sửa Chuyên mục”
  When tôi nhập meta description ≤ 160 ký tự và nhấn "Lưu"
  Then meta description được lưu và trả về trong API `/categories/:id`

Scenario: Slug trùng lặp
  Given slug "tin-tuc" đã tồn tại
  When tôi nhập slug "tin-tuc"
  Then hệ thống cảnh báo "Slug đã tồn tại" và không cho lưu
```

**DoD**

-

---

### 3.2 CMS‑EP2 – Quản lý Tin bài

| ID              | User Story                                                                                                | Ưu tiên | Điểm |
| --------------- | --------------------------------------------------------------------------------------------------------- | ------- | ---- |
| **CMS‑EP2‑US1** | *Là* **Author**, tôi muốn **soạn thảo bài mới với trình soạn thảo WYSIWYG** để định dạng văn bản dễ dàng. | H       | 8    |

**AC**

```
Scenario: Lưu nháp thành công
  Given tôi soạn nội dung ≥ 50 ký tự
  When tôi nhấn "Lưu nháp"
  Then bài viết được lưu với trạng thái "Draft" 

Scenario: Định dạng rich‑text
  Given tôi chọn đoạn văn bản
  When tôi nhấn icon "Bold"
  Then đoạn văn được bôi đậm trong editor và trong preview
```

**DoD**

-

\| **CMS‑EP2‑US2** | *Là* **Author**, tôi muốn **lưu bản nháp** để tiếp tục chỉnh sửa sau. | H | 3 |

**AC**

```
Scenario: Tự động lưu 5 phút/lần
  Given tôi đang soạn thảo
  When 5 phút trôi qua
  Then hệ thống tự lưu bản nháp và hiện thông báo "Đã lưu lúc 10:05"
```

**DoD**

-

\| **CMS‑EP2‑US3** | *Là* **Author**, tôi muốn **gắn ảnh/video** vào bài viết để nội dung sinh động. | H | 5 |

**AC**

```
Scenario: Chèn ảnh từ thư viện
  Given tôi mở popup "Chèn ảnh"
  When tôi chọn ảnh "team.jpg" và nhấn "Insert"
  Then ảnh hiển thị trong editor với kích thước mặc định 640x auto

Scenario: Upload video mp4 > 100MB
  Given file video 120 MB
  When tôi upload
  Then hệ thống thông báo "Dung lượng vượt quá 100 MB"
```

**DoD**

-

\| **CMS‑EP2‑US4** | *Là* **Author**, tôi muốn **gắn thẻ (tag) cho bài** để tối ưu tìm kiếm. | M | 3 |

**AC**

```
Scenario: Thêm tag mới
  Given ô "Tags"
  When tôi gõ "Agile" và nhấn Enter
  Then tag "Agile" được thêm và xuất hiện trong danh sách tag toàn hệ thống
```

**DoD**

-

\| **CMS‑EP2‑US5** | *Là* **Editor**, tôi muốn **lập lịch đăng bài** để bài tự xuất bản vào thời gian mong muốn. | H | 5 |

**AC**

```
Scenario: Lên lịch < thời gian hiện tại
  Given bây giờ 09:00 29/07/2025
  When tôi đặt lịch 08:00 29/07/2025
  Then hệ thống báo lỗi "Thời gian phải lớn hơn hiện tại"

Scenario: Bài tự xuất bản
  Given bài đã lên lịch 10:00 29/07/2025
  When đồng hồ hệ thống đạt 10:00
  Then trạng thái bài chuyển thành "Published" và hiển thị trên trang công khai
```

**DoD**

-

---

### 3.3 CMS‑EP3 – Phê duyệt & Xuất bản

| ID              | User Story                                                                         | Ưu tiên | Điểm |
| --------------- | ---------------------------------------------------------------------------------- | ------- | ---- |
| **CMS‑EP3‑US1** | *Là* **Author**, tôi muốn **gửi bài nháp cho cấp duyệt** để được xem xét xuất bản. | H       | 3    |

**AC**

```
Scenario: Gửi bài thành công
  Given bài ở trạng thái "Draft"
  When tôi nhấn "Submit for review"
  Then bài chuyển sang trạng thái "Pending Review" và người duyệt nhận thông báo
```

**DoD**

-

\| **CMS‑EP3‑US2** | *Là* **Editor**, tôi muốn **phê duyệt hoặc từ chối bài** và ghi nhận xét để đảm bảo chất lượng nội dung. | H | 5 |

**AC**

```
Scenario: Duyệt bài
  Given bài trạng thái "Pending Review"
  When tôi chọn "Approve" và nhập comment "OK"
  Then bài chuyển sang "Approved" và Author nhận thông báo

Scenario: Từ chối bài
  Given bài trạng thái "Pending Review"
  When tôi chọn "Reject" và nhập lý do "Viết thiếu nguồn"
  Then bài chuyển sang "Rejected" với comment hiển thị cho Author
```

**DoD**

-

\| **CMS‑EP3‑US3** | *Là* **Editor**, tôi muốn **xem nhật ký phê duyệt** để biết ai đã chỉnh sửa gì. | M | 3 |

**AC**

```
Scenario: Hiển thị lịch sử
  Given tôi ở tab "History"
  When bài có 3 lần duyệt
  Then danh sách log hiển thị theo thời gian giảm dần gồm hành động, người thực hiện, timestamp
```

**DoD**

-

\| **CMS‑EP3‑US4** | *Là* **System**, tôi muốn **gửi thông báo khi có bài chờ duyệt** để người liên quan xử lý kịp thời. | H | 5 |

**AC**

```
Scenario: Push notification
  Given có bài mới Pending Review
  When sự kiện được ghi nhận
  Then hệ thống gửi notification tới nhóm "Editors" trong vòng 5 giây
```

**DoD**

-

---

### 3.4 CMS‑EP4 – Thư viện Media

| ID              | User Story                                                                            | Ưu tiên | Điểm |
| --------------- | ------------------------------------------------------------------------------------- | ------- | ---- |
| **CMS‑EP4‑US1** | *Là* **Admin**, tôi muốn **upload nhiều file media cùng lúc** để tiết kiệm thời gian. | H       | 5    |

**AC**

```
Scenario: Drag‑and‑drop 10 ảnh
  Given trang "Thư viện Media"
  When tôi kéo thả 10 file jpg tổng 30 MB
  Then toàn bộ file được upload song song và hiển thị thumbnail
```

**DoD**

-

\| **CMS‑EP4‑US2** | *Là* **Editor**, tôi muốn **tìm kiếm và chèn media đã có** để tái sử dụng tài sản số. | H | 3 |

**AC**

```
Scenario: Tìm kiếm theo tên file
  Given ô tìm kiếm media
  When tôi nhập "banner"
  Then danh sách trả về file có tên chứa "banner" (case‑insensitive)
```

**DoD**

-

\| **CMS‑EP4‑US3** | *Là* **Editor**, tôi muốn **tạo bộ sưu tập media** để nhóm ảnh theo chủ đề. | M | 5 |

**AC**

```
Scenario: Thêm media vào collection
  Given collection "Tết 2025" tồn tại
  When tôi chọn 5 ảnh và nhấn "Add to collection: Tết 2025"
  Then 5 ảnh được liên kết với collection đó
```

**DoD**

-

---

### 3.5 CMS‑EP5 – Người dùng & Phân quyền

| ID              | User Story                                                                                  | Ưu tiên | Điểm |
| --------------- | ------------------------------------------------------------------------------------------- | ------- | ---- |
| **CMS‑EP5‑US1** | *Là* **Admin**, tôi muốn **tạo/sửa/xóa tài khoản người dùng** để quản lý truy cập hệ thống. | H       | 3    |

**AC**

```
Scenario: Tạo user mới
  Given form "Tạo người dùng"
  When tôi điền email hợp lệ & vai trò
  Then tài khoản được gửi email kích hoạt
```

**DoD**

-

\| **CMS‑EP5‑US2** | *Là* **Admin**, tôi muốn **gán vai trò** (Admin, Editor, Author, Viewer) để phân quyền phù hợp. | H | 5 |

**AC**

```
Scenario: Đổi vai trò Author → Editor
  Given user "Nam" đang là Author
  When tôi chọn "Editor" và nhấn "Lưu"
  Then quyền truy cập module phê duyệt được cấp ngay
```

**DoD**

-

\| **CMS‑EP5‑US3** | *Là* **Security Officer**, tôi muốn **tra cứu nhật ký đăng nhập** để phát hiện bất thường. | M | 5 |

**AC**

```
Scenario: Tìm theo IP
  Given trang "Nhật ký đăng nhập"
  When tôi nhập IP "************"
  Then danh sách phiên đăng nhập từ IP đó hiển thị
```

**DoD**

-

---

### 3.6 CMS‑EP6 – Tìm kiếm & Lọc

| ID              | User Story                                                                                              | Ưu tiên | Điểm |
| --------------- | ------------------------------------------------------------------------------------------------------- | ------- | ---- |
| **CMS‑EP6‑US1** | *Là* **User**, tôi muốn **tìm kiếm tin, chuyên mục, media bằng ô tìm kiếm toàn cục** để truy cập nhanh. | H       | 8    |

**AC**

```
Scenario: Autocomplete kết quả
  Given tôi gõ "AI"
  Then dropdown gợi ý 5 kết quả bài viết, chuyên mục, media chứa từ "AI"
```

**DoD**

-

\| **CMS‑EP6‑US2** | *Là* **Editor**, tôi muốn **lọc bài theo trạng thái** (nháp, chờ duyệt...), để quản lý công việc. | H | 3 |

**AC**

```
Scenario: Lọc bài Pending Review
  Given dropdown "Trạng thái"
  When tôi chọn "Pending Review"
  Then chỉ hiển thị bài ở trạng thái đó
```

**DoD**

-

---

### 3.7 CMS‑EP7 – Hiển thị & SEO

| ID              | User Story                                                                               | Ưu tiên | Điểm |
| --------------- | ---------------------------------------------------------------------------------------- | ------- | ---- |
| **CMS‑EP7‑US1** | *Là* **Editor**, tôi muốn **đánh dấu bài "Nổi bật"** để ưu tiên hiển thị trên trang chủ. | M       | 3    |

**AC**

```
Scenario: Bài nổi bật hiển thị slider
  Given tôi đánh dấu "Featured"
  When tôi xem trang chủ
  Then bài ở vị trí slider đầu tiên
```

**DoD**

-

\| **CMS‑EP7‑US2** | *Là* **SEO Specialist**, tôi muốn **hệ thống tự sinh meta title & description** để tối ưu SEO. | H | 5 |

**AC**

```
Scenario: Auto gen meta khi không nhập
  Given bài viết "Du lịch Huế"
  When description trống
  Then hệ thống sinh meta description 155 ký tự đầu tiên từ nội dung không chứa HTML
```

**DoD**

-

\| **CMS‑EP7‑US3** | *Là* **Editor**, tôi muốn **xem trước (preview) bài trên web & mobile** để đảm bảo trình bày. | M | 5 |

**AC**

```
Scenario: Preview chế độ mobile
  Given tôi nhấn "Preview"
  When chọn "Mobile"
  Then bài hiển thị trong khung giả lập 375px width
```

**DoD**

-

---

### 3.8 CMS‑EP8 – Giao diện Trang chủ/Kênh/Chi tiết

| ID              | User Story                                                                                       | Ưu tiên | Điểm |
| --------------- | ------------------------------------------------------------------------------------------------ | ------- | ---- |
| **CMS‑EP8‑US1** | *Là* **Frontend Dev**, tôi muốn **cấu hình layout trang chủ cố định** để thống nhất thương hiệu. | H       | 5    |

**AC**

```
Scenario: Chọn layout "Magazine"
  Given trang "Cấu hình Layout"
  When tôi chọn template "Magazine" và nhấn "Áp dụng"
  Then giao diện trang chủ chuyển sang layout đã chọn
```

**DoD**

-

\| **CMS‑EP8‑US2** | *Là* **Frontend Dev**, tôi muốn **quản lý layout trang kênh & chuyên mục** để hiển thị nội dung động. | H | 5 |

**AC**

```
Scenario: Thêm widget "Top Trending"
  Given trang cấu hình kênh "Công nghệ"
  When tôi drag‑and‑drop widget "Top Trending" vào vị trí #2
  Then frontend hiển thị widget đó ở vị trí đã chọn
```

**DoD**

-

\| **CMS‑EP8‑US3** | *Là* **Frontend Dev**, tôi muốn **template trang chi tiết bài viết chuẩn AMP** để tối ưu tốc độ mobile. | M | 8 |

**AC**

```
Scenario: AMP validation pass
  Given tôi publish bài chi tiết
  When tôi kiểm tra URL qua validator.ampproject.org
  Then kết quả hiển thị "PASS"
```

**DoD**

-

---

## 4. Bảng sắp xếp Product Backlog (MoSCoW)

| Priority        | User Story IDs                                                                           |
| --------------- | ---------------------------------------------------------------------------------------- |
| **Must**        | EP1‑US1, EP1‑US3, EP2‑US1, EP2‑US2, EP3‑US1, EP3‑US2, EP4‑US1, EP5‑US1, EP5‑US2, EP6‑US1 |
| **Should**      | EP1‑US2, EP1‑US4, EP2‑US3, EP2‑US5, EP3‑US4, EP4‑US2, EP6‑US2, EP7‑US2, EP8‑US1, EP8‑US2 |
| **Could**       | EP2‑US4, EP3‑US3, EP4‑US3, EP5‑US3, EP7‑US1, EP7‑US3, EP8‑US3                            |
| **Won’t (Yet)** | (trống)                                                                                  |

---

## 5. Định nghĩa Sẵn sàng (Definition of Ready)

1. User Story mô tả theo khuôn mẫu “Là … tôi muốn … để …”.
2. Acceptance Criteria đủ Scenario chính, rõ ràng đo lường.
3. Estimation hoàn tất & phụ thuộc được ghi nhận.
4. Mockup/UI (nếu có) sẵn sàng.

---

## 6. Định nghĩa Hoàn thành (Definition of Done) – Toàn dự án

- Pull request đã được review ≥ 2 thành viên và merge.
- 100% checklist DoD riêng của story ✅.
- Toàn bộ test (unit, integration, e2e) **pass**; code coverage ≥ 90%.
- QA xác nhận UAT & không bug blocker.
- Tài liệu release note & user guide cập nhật.

---

## 7. Phụ lục

- **Giả định**: Hệ thống microservice + cloud storage cho media.
- **Giới hạn**: Không tích hợp AI tự động sinh nội dung ở phiên bản 1.1.
- **Nguồn yêu cầu**: Tài liệu “Phân tích chức năng CMS” (PO).

