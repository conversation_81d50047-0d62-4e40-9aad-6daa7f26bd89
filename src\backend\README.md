# Terragon Backend

Backend application được xây dựng theo kiến trúc DDD/Clean Architecture với Spring Boot.

## Tech Stack

- **Java 21**
- **Spring Boot 3.2.0**
- **PostgreSQL** (Production) / **H2** (Development)
- **Keycloak** (Authentication)
- **MinIO** (File Storage)
- **Maven** (Build Tool)

## Cấu trúc dự án

```
src/main/java/com/terragon/
├── domain/          # Domain Layer
├── application/     # Application Layer  
├── infrastructure/  # Infrastructure Layer
└── presentation/    # Presentation Layer
```

Chi tiết xem tại: [DDD/Clean Architecture Documentation](../../docs/architecture/ddd-clean-architecture.md)

## Yêu cầu hệ thống

- **Java 21+**
- **Maven 3.8+**
- **PostgreSQL 13+** (Production)
- **Keycloak Server** (Authentication)
- **MinIO Server** (File Storage)

## Cài đặt và chạy

### 1. Development Mode (H2 Database)

```bash
cd src/backend
mvn spring-boot:run -Dspring.profiles.active=dev
```

### 2. Production Mode (PostgreSQL)

```bash
# Cấu hình database trong application.properties
cd src/backend
mvn clean install
mvn spring-boot:run
```

## API Documentation

### User Management
- `POST /api/users` - Tạo user mới
- `GET /api/users/{id}` - Lấy user theo ID
- `GET /api/users` - Danh sách users

### File Management  
- `POST /api/files/upload` - Upload file
- `GET /api/files/download/{fileName}` - Download file

## Cấu hình môi trường

### Development
```properties
spring.profiles.active=dev
```

### Production
Cập nhật các thông tin kết nối trong `application.properties`:
- Database PostgreSQL
- Keycloak server
- MinIO server

## Testing

```bash
mvn test
```

## Build

```bash
mvn clean install
```