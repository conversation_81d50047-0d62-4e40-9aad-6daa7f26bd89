# Application Configuration
spring.application.name=terragon-backend
server.port=8080
spring.main.banner-mode=off

# Database Configuration - PostgreSQL
spring.datasource.url=********************************************
spring.datasource.username=terragon_user
spring.datasource.password=${DB_PASSWORD:terragon_password}
spring.datasource.driver-class-name=org.postgresql.Driver

# JPA/Hibernate Configuration
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect
spring.jpa.properties.hibernate.jdbc.lob.non_contextual_creation=true

# Keycloak Configuration
keycloak.realm=terragon-realm
keycloak.auth-server-url=http://localhost:8080/auth
keycloak.resource=terragon-backend
keycloak.public-client=false
keycloak.credentials.secret=${KEYCLOAK_CLIENT_SECRET:your-client-secret}

# MinIO Configuration
minio.endpoint=http://localhost:9000
minio.access-key=minioadmin
minio.secret-key=minioadmin
minio.bucket-name=terragon-bucket

# Logging Configuration
logging.level.root=INFO
logging.level.com.terragon=DEBUG
logging.level.org.springframework.security=DEBUG
logging.level.org.keycloak=DEBUG
logging.level.jdbc=OFF
logging.level.jdbc.sqltiming=DEBUG
logging.level.jdbc.resultsettable=DEBUG

# Actuator Configuration
management.endpoints.web.exposure.include=health,info,metrics,prometheus,loggers,env
management.endpoint.health.show-details=always
management.endpoint.health.probes.enabled=true
management.health.livenessstate.enabled=true
management.health.readinessstate.enabled=true

# Metrics Configuration
management.metrics.export.prometheus.enabled=true
management.metrics.distribution.percentiles-histogram.http.server.requests=true
management.metrics.distribution.percentiles.http.server.requests=0.5,0.9,0.95,0.99
management.metrics.tags.application=terragon-backend

# Tracing Configuration (OpenTelemetry)
management.tracing.sampling.probability=1.0
management.zipkin.tracing.endpoint=http://localhost:9411/api/v2/spans

# JWT Configuration
jwt.secret=${JWT_SECRET:myVerySecretKeyForJWTTokenGeneration2024!@#}
jwt.expiration=86400000
jwt.refresh.expiration=604800000

# File Upload Configuration
spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=10MB
