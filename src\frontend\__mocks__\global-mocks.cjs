/**
 * Global mock implementations cho frontend testing
 * Centralized mocks để đảm bảo consistency across all tests
 */

// Mock React Slot component từ Radix UI
const mockSlot = () => {
  const mockReact = require('react');
  return {
    Slot: mockReact.forwardRef((props, ref) => {
      const { children, ...rest } = props;
      return mockReact.createElement('div', { 
        ref, 
        'data-testid': 'radix-slot',
        'data-slot': true,
        ...rest 
      }, children);
    }),
  };
};

// Mock class-variance-authority
const mockCva = () => ({
  cva: jest.fn((baseClasses, config) => {
    return jest.fn((variants = {}) => {
      const classes = [baseClasses];
      
      if (config?.variants) {
        Object.entries(variants).forEach(([key, value]) => {
          if (value && config.variants[key]?.[value]) {
            classes.push(`${key}-${value}`);
          }
        });
      }
      
      // Apply default variants if not provided
      if (config?.defaultVariants) {
        Object.entries(config.defaultVariants).forEach(([key, defaultValue]) => {
          if (!(key in variants)) {
            classes.push(`${key}-${defaultValue}`);
          }
        });
      }
      
      if (variants.className) {
        classes.push(variants.className);
      }
      
      return classes.filter(Boolean).join(' ');
    });
  }),
  type: {} as any,
});

// Mock cn utility function
const mockCn = () => ({
  cn: jest.fn((...classes) => 
    classes
      .flat()
      .filter(Boolean)
      .join(' ')
  ),
});

// Mock Next.js utilities
const mockNextRouter = () => ({
  useRouter: () => ({
    push: jest.fn(),
    replace: jest.fn(),
    back: jest.fn(),
    forward: jest.fn(),
    refresh: jest.fn(),
    pathname: '/',
    query: {},
    asPath: '/',
    events: {
      on: jest.fn(),
      off: jest.fn(),
      emit: jest.fn(),
    },
  }),
});

const mockNextNavigation = () => ({
  useRouter: () => ({
    push: jest.fn(),
    replace: jest.fn(),
    back: jest.fn(),
    forward: jest.fn(),
    refresh: jest.fn(),
  }),
  usePathname: () => '/',
  useSearchParams: () => new URLSearchParams(),
});

// Setup global mocks
global.mockSlot = mockSlot;
global.mockCva = mockCva;
global.mockCn = mockCn;
global.mockNextRouter = mockNextRouter;
global.mockNextNavigation = mockNextNavigation;

module.exports = {
  mockSlot,
  mockCva,
  mockCn,
  mockNextRouter,
  mockNextNavigation,
};