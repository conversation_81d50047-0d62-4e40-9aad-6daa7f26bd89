/**
 * Test utilities và helpers cho frontend testing
 * Cung cấp reusable functions để improve test quality và maintainability
 */

import { ReactElement } from 'react';
import { render, RenderOptions, RenderResult } from '@testing-library/react';
import userEvent from '@testing-library/user-event';

// Re-export everything from testing-library
export * from '@testing-library/react';
export { userEvent };

/**
 * Custom render function với default setup
 */
export interface CustomRenderOptions extends Omit<RenderOptions, 'wrapper'> {
  withRouter?: boolean;
  initialRoute?: string;
}

export const renderWithDefaults = (
  ui: ReactElement,
  options: CustomRenderOptions = {}
): RenderResult => {
  const { withRouter = false, initialRoute = '/', ...renderOptions } = options;

  // Có thể extend với providers khác nếu cần
  let wrapper = undefined;

  if (withRouter) {
    // Implement router wrapper nếu cần trong tương lai
    throw new Error('Router wrapper chưa đ<PERSON>ợ<PERSON> implement. <PERSON><PERSON> lò<PERSON> implement trước khi sử dụng.');
  }

  return render(ui, { wrapper, ...renderOptions });
};

/**
 * Helper để tạo mock user object
 */
export const createMockUser = (overrides = {}) => ({
  id: '1',
  email: '<EMAIL>',
  name: 'Test User',
  ...overrides,
});

/**
 * Helper để tạo mock event handlers
 */
export const createMockHandlers = () => ({
  onClick: jest.fn(),
  onChange: jest.fn(),
  onSubmit: jest.fn(),
  onFocus: jest.fn(),
  onBlur: jest.fn(),
});

/**
 * Helper để wait for async operations trong tests
 */
export const waitForNextTick = () => new Promise(resolve => setTimeout(resolve, 0));

/**
 * Helper để assert error boundaries
 */
export const expectToThrow = async (fn: () => void | Promise<void>, expectedError?: string) => {
  let error: Error | undefined;
  
  try {
    await fn();
  } catch (e) {
    error = e as Error;
  }
  
  expect(error).toBeDefined();
  if (expectedError) {
    expect(error?.message).toContain(expectedError);
  }
};

/**
 * Helper để test accessibility
 */
export const testA11y = {
  hasRole: (element: Element, role: string) => {
    expect(element).toHaveAttribute('role', role);
  },
  hasAriaLabel: (element: Element, label: string) => {
    expect(element).toHaveAttribute('aria-label', label);
  },
  isFocusable: (element: Element) => {
    const focusableElements = ['button', 'a', 'input', 'select', 'textarea'];
    const tagName = element.tagName.toLowerCase();
    const hasTabIndex = element.hasAttribute('tabindex');
    const isNaturallyFocusable = focusableElements.includes(tagName);
    expect(hasTabIndex || isNaturallyFocusable).toBe(true);
  },
  hasKeyboardSupport: async (element: Element, key: string) => {
    const user = userEvent.setup();
    element.focus();
    await user.keyboard(key);
  },
};

/**
 * Helper để test component variants
 */
export const testVariants = <T extends Record<string, any>>(
  Component: React.ComponentType<T>,
  baseProps: T,
  variants: Array<{ name: string; props: Partial<T>; expectation: (element: Element) => void }>
) => {
  variants.forEach(({ name, props, expectation }) => {
    it(`nên render correctly với variant: ${name}`, () => {
      const { container } = render(<Component {...baseProps} {...props} />);
      const element = container.firstChild as Element;
      expectation(element);
    });
  });
};

/**
 * Helper để group related tests
 */
export const describeFeature = (featureName: string, tests: () => void) => {
  describe(`Feature: ${featureName}`, tests);
};

export const describeComponent = (componentName: string, tests: () => void) => {
  describe(`Component: ${componentName}`, tests);
};

export const describeBehavior = (behaviorName: string, tests: () => void) => {
  describe(`Behavior: ${behaviorName}`, tests);
};

/**
 * Helper để test error states
 */
export const testErrorStates = <T extends Record<string, any>>(
  Component: React.ComponentType<T>,
  baseProps: T,
  errorScenarios: Array<{ 
    name: string; 
    props: Partial<T>; 
    expectedError: string;
  }>
) => {
  describe('Error States', () => {
    errorScenarios.forEach(({ name, props, expectedError }) => {
      it(`nên handle error: ${name}`, async () => {
        await expectToThrow(
          () => render(<Component {...baseProps} {...props} />),
          expectedError
        );
      });
    });
  });
};

/**
 * Helper để test loading states
 */
export const testLoadingStates = <T extends Record<string, any>>(
  Component: React.ComponentType<T>,
  baseProps: T,
  loadingProps: Partial<T>
) => {
  describe('Loading States', () => {
    it('nên display loading state correctly', () => {
      render(<Component {...baseProps} {...loadingProps} />);
      // Add specific loading state assertions
    });
  });
};