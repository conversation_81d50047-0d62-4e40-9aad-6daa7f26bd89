{"name": "web", "version": "0.0.1", "type": "module", "private": true, "scripts": {"dev": "next dev --turbopack -p 3000", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "typecheck": "tsc --noEmit", "test": "jest", "test:coverage": "jest --coverage", "test:watch": "jest --watch"}, "dependencies": {"@workspace/api-client": "workspace:^", "@workspace/auth": "workspace:^", "@workspace/lib": "workspace:^", "@workspace/store": "workspace:^", "@workspace/types": "workspace:^", "@workspace/ui": "workspace:*", "lucide-react": "^0.475.0", "next": "^15.2.3", "next-themes": "^0.4.4", "react": "^19.0.0", "react-dom": "^19.0.0"}, "devDependencies": {"@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@workspace/eslint-config": "workspace:^", "@workspace/typescript-config": "workspace:*", "typescript": "^5.7.3"}}