"use client";

import * as React from "react";
import { ThemeProvider as NextThemesProvider } from "next-themes";

/**
 * Props cho Providers component
 */
interface ProvidersProps {
  /** React components con */
  children: React.ReactNode;
}

/**
 * Component Providers cung cấp các provider context cho toàn bộ web app
 * Hiện tại bao gồm NextThemesProvider để hỗ trợ dark/light mode
 * 
 * @param children - React components con sẽ được wrap bởi các providers
 */
export function Providers({ children }: ProvidersProps) {
  return (
    <NextThemesProvider
      attribute="class"
      defaultTheme="system"
      enableSystem
      disableTransitionOnChange
      enableColorScheme
    >
      {children}
    </NextThemesProvider>
  );
}
