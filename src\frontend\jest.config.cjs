/** @type {import('jest').Config} */
module.exports = {
  projects: [
    '<rootDir>/apps/*/jest.config.cjs',
    '<rootDir>/packages/*/jest.config.cjs',
  ],
  collectCoverageFrom: [
    'apps/**/src/**/*.{ts,tsx}',
    'packages/**/src/**/*.{ts,tsx}',
    '!**/*.d.ts',
    '!**/node_modules/**',
    '!**/dist/**',
    '!**/.next/**',
    '!**/coverage/**',
    '!**/*stories.{ts,tsx}',
    '!**/index.{ts,tsx}',
    '!**/__tests__/**',
    '!**/__mocks__/**',
  ],
  coverageThreshold: {
    global: {
      lines: 90,
      branches: 75,
      functions: 90,
      statements: 90,
    },
  },
  coverageReporters: ['text', 'lcov', 'html', 'json'],
  coverageDirectory: '<rootDir>/coverage',
  collectCoverage: false, // Only collect when explicitly requested
};