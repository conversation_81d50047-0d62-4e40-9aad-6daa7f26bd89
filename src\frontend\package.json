{"name": "frontend-cms", "version": "0.0.1", "private": true, "scripts": {"build": "turbo build", "dev": "turbo dev", "lint": "turbo lint", "typecheck": "turbo check-types", "test": "turbo test", "test:coverage": "turbo test:coverage", "format": "prettier --write '**/*.{js,jsx,ts,tsx,md,mdx,json,yml,yaml,css,scss}'"}, "devDependencies": {"@babel/core": "^7.28.0", "@babel/preset-env": "^7.28.0", "@babel/preset-react": "^7.27.1", "@babel/preset-typescript": "^7.27.1", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.1.0", "@testing-library/user-event": "^14.5.2", "@types/jest": "^29.5.14", "@workspace/eslint-config": "workspace:*", "@workspace/typescript-config": "workspace:*", "babel-jest": "^29.7.0", "eslint": "^8.57.1", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "prettier": "^3.5.1", "turbo": "^2.4.2", "typescript": "5.7.3"}, "packageManager": "pnpm@10.4.1", "engines": {"node": ">=20"}}