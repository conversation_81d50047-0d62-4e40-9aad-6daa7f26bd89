/**
 * Service xử lý các API calls liên quan đến authentication
 * Chứa các function gọi API đăng nhập, đăng xuất, refresh token, etc.
 */

export interface LoginRequest {
  email: string;
  password: string;
}

export interface LoginResponse {
  accessToken: string;
  refreshToken: string;
  user: {
    id: string;
    email: string;
    name: string;
  };
}

export interface RefreshTokenRequest {
  refreshToken: string;
}

export interface RefreshTokenResponse {
  accessToken: string;
  refreshToken: string;
}

export interface LogoutRequest {
  refreshToken: string;
}

// HTTP client - sẽ được replaced bằng actual implementation hoặc mock trong tests
const httpClient = {
  post: async (url: string, data: any): Promise<{ data: any }> => {
    // This should be replaced with actual HTTP client (axios, fetch, etc.) in production
    throw new Error('HTTP client not implemented. This should be mocked in tests or replaced with actual implementation.');
  }
};

/**
 * Đăng nhập user với email và password
 * @param credentials - Thông tin đăng nhập
 * @returns Promise với response chứa tokens và user info
 */
export const login = async (credentials: LoginRequest): Promise<LoginResponse> => {
  try {
    const response = await httpClient.post('/api/auth/login', credentials);
    return response.data;
  } catch (error) {
    throw new Error(`Login failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
};

/**
 * Refresh access token sử dụng refresh token
 * @param request - Request chứa refresh token
 * @returns Promise với tokens mới
 */
export const refreshToken = async (request: RefreshTokenRequest): Promise<RefreshTokenResponse> => {
  try {
    const response = await httpClient.post('/api/auth/refresh', request);
    return response.data;
  } catch (error) {
    throw new Error(`Token refresh failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
};

/**
 * Đăng xuất user và invalidate refresh token
 * @param request - Request chứa refresh token
 * @returns Promise với success status
 */
export const logout = async (request: LogoutRequest): Promise<{ success: boolean }> => {
  try {
    const response = await httpClient.post('/api/auth/logout', request);
    return response.data;
  } catch (error) {
    throw new Error(`Logout failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
};

/**
 * Validate email format
 * @param email - Email to validate
 * @returns boolean indicating if email is valid
 */
export const validateEmail = (email: string): boolean => {
  if (!email || typeof email !== 'string') {
    return false;
  }
  
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

/**
 * Validate password strength
 * @param password - Password to validate
 * @returns object with validation result and messages
 */
export const validatePassword = (password: string): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];
  
  if (!password || typeof password !== 'string') {
    errors.push('Password is required');
    return { isValid: false, errors };
  }
  
  if (password.length < 8) {
    errors.push('Password must be at least 8 characters long');
  }
  
  if (!/[A-Z]/.test(password)) {
    errors.push('Password must contain at least one uppercase letter');
  }
  
  if (!/[a-z]/.test(password)) {
    errors.push('Password must contain at least one lowercase letter');
  }
  
  if (!/[0-9]/.test(password)) {
    errors.push('Password must contain at least one number');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
};
