import React from 'react';
import { renderHook, act } from '@testing-library/react';
import { render, screen } from '@testing-library/react';
import { useAuth } from '../use-auth';
import { AuthProvider, AuthContext } from '../../providers/auth-provider';
import { UserModel } from '../../types/models/user.model';

/**
 * Mock user data dùng cho các test case
 */
const mockUserData: UserModel = {
  id: 1,
  username: 'testuser',
  email: '<EMAIL>',
  firstName: 'Test',
  lastName: 'User'
};

/**
 * Wrapper component cho các test cases cần AuthProvider
 */
const AuthProviderWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <AuthProvider>{children}</AuthProvider>
);

/**
 * Component test helper để test useAuth hook trong render context
 */
const TestComponent: React.FC = () => {
  const auth = useAuth();
  
  return (
    <div>
      <span data-testid="user-data">
        {auth.user ? JSON.stringify(auth.user) : 'No user'}
      </span>
      <span data-testid="user-id">
        {auth.user?.id || 'No ID'}
      </span>
      <span data-testid="username">
        {auth.user?.username || 'No username'}
      </span>
      <span data-testid="email">
        {auth.user?.email || 'No email'}
      </span>
      <span data-testid="first-name">
        {auth.user?.firstName || 'No firstName'}
      </span>
      <span data-testid="last-name">
        {auth.user?.lastName || 'No lastName'}
      </span>
      <button 
        data-testid="login-btn"
        onClick={() => auth.login(mockUserData)}
      >
        Login
      </button>
      <button 
        data-testid="logout-btn"
        onClick={() => auth.logout()}
      >
        Logout
      </button>
    </div>
  );
};

describe('useAuth Hook', () => {
  describe('Hook Usage Within AuthProvider', () => {
    it('nên return auth context correctly khi được sử dụng trong AuthProvider', () => {
      const { result } = renderHook(() => useAuth(), {
        wrapper: AuthProviderWrapper,
      });

      expect(result.current).toBeDefined();
      expect(result.current.user).toBeNull();
      expect(typeof result.current.login).toBe('function');
      expect(typeof result.current.logout).toBe('function');
    });

    it('nên return context với các function references ổn định', () => {
      const { result, rerender } = renderHook(() => useAuth(), {
        wrapper: AuthProviderWrapper,
      });

      const initialLogin = result.current.login;
      const initialLogout = result.current.logout;

      rerender();

      // Functions should maintain their references across re-renders
      expect(typeof result.current.login).toBe('function');
      expect(typeof result.current.logout).toBe('function');
    });

    it('nên access được tất cả các properties từ AuthContextType', () => {
      const { result } = renderHook(() => useAuth(), {
        wrapper: AuthProviderWrapper,
      });

      const authContext = result.current;

      // Kiểm tra tất cả required properties tồn tại
      expect(authContext).toHaveProperty('user');
      expect(authContext).toHaveProperty('login');
      expect(authContext).toHaveProperty('logout');

      // Kiểm tra types
      expect(authContext.user === null || typeof authContext.user === 'object').toBe(true);
      expect(typeof authContext.login).toBe('function');
      expect(typeof authContext.logout).toBe('function');
    });
  });

  describe('Hook Usage Outside AuthProvider', () => {
    it('nên throw error khi được sử dụng outside AuthProvider', () => {
      // Mock console.error để tránh error logs trong test
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});

      expect(() => {
        renderHook(() => useAuth());
      }).toThrow('useAuth must be used within an AuthProvider');

      consoleSpy.mockRestore();
    });

    it('nên throw correct error message', () => {
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});

      try {
        renderHook(() => useAuth());
      } catch (error) {
        expect(error).toBeInstanceOf(Error);
        expect((error as Error).message).toBe('useAuth must be used within an AuthProvider');
      }

      consoleSpy.mockRestore();
    });

    it('nên throw error ngay cả khi có nested components', () => {
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});

      const NestedComponent: React.FC = () => {
        const auth = useAuth();
        return <div>{auth.user?.username}</div>;
      };

      const WrapperWithoutProvider: React.FC = () => (
        <div>
          <NestedComponent />
        </div>
      );

      expect(() => {
        render(<WrapperWithoutProvider />);
      }).toThrow('useAuth must be used within an AuthProvider');

      consoleSpy.mockRestore();
    });
  });

  describe('Return Values from Context', () => {
    it('nên return initial state với user null', () => {
      const { result } = renderHook(() => useAuth(), {
        wrapper: AuthProviderWrapper,
      });

      expect(result.current.user).toBeNull();
    });

    it('nên return updated user data sau khi login', () => {
      const { result } = renderHook(() => useAuth(), {
        wrapper: AuthProviderWrapper,
      });

      act(() => {
        result.current.login(mockUserData);
      });

      expect(result.current.user).toEqual(mockUserData);
      expect(result.current.user?.id).toBe(1);
      expect(result.current.user?.username).toBe('testuser');
      expect(result.current.user?.email).toBe('<EMAIL>');
      expect(result.current.user?.firstName).toBe('Test');
      expect(result.current.user?.lastName).toBe('User');
    });

    it('nên return null user sau khi logout', () => {
      const { result } = renderHook(() => useAuth(), {
        wrapper: AuthProviderWrapper,
      });

      // Login trước
      act(() => {
        result.current.login(mockUserData);
      });

      expect(result.current.user).toEqual(mockUserData);

      // Logout
      act(() => {
        result.current.logout();
      });

      expect(result.current.user).toBeNull();
    });

    it('nên return đúng các function references', () => {
      const { result } = renderHook(() => useAuth(), {
        wrapper: AuthProviderWrapper,
      });

      expect(result.current.login).toBeInstanceOf(Function);
      expect(result.current.logout).toBeInstanceOf(Function);

      // Test function calls không throw error
      expect(() => {
        act(() => {
          result.current.login(mockUserData);
        });
      }).not.toThrow();

      expect(() => {
        act(() => {
          result.current.logout();
        });
      }).not.toThrow();
    });
  });

  describe('Hook Integration with Components', () => {
    it('nên hoạt động correctly trong React component context', () => {
      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      );

      // Initial state
      expect(screen.getByTestId('user-data')).toHaveTextContent('No user');
      expect(screen.getByTestId('user-id')).toHaveTextContent('No ID');
      expect(screen.getByTestId('username')).toHaveTextContent('No username');
      expect(screen.getByTestId('email')).toHaveTextContent('No email');
      expect(screen.getByTestId('first-name')).toHaveTextContent('No firstName');
      expect(screen.getByTestId('last-name')).toHaveTextContent('No lastName');
    });

    it('nên update UI khi auth state thay đổi', () => {
      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      );

      const loginButton = screen.getByTestId('login-btn');

      // Login
      act(() => {
        loginButton.click();
      });

      expect(screen.getByTestId('user-data')).toHaveTextContent(JSON.stringify(mockUserData));
      expect(screen.getByTestId('user-id')).toHaveTextContent('1');
      expect(screen.getByTestId('username')).toHaveTextContent('testuser');
      expect(screen.getByTestId('email')).toHaveTextContent('<EMAIL>');
      expect(screen.getByTestId('first-name')).toHaveTextContent('Test');
      expect(screen.getByTestId('last-name')).toHaveTextContent('User');
    });

    it('nên reset UI khi logout', () => {
      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      );

      const loginButton = screen.getByTestId('login-btn');
      const logoutButton = screen.getByTestId('logout-btn');

      // Login trước
      act(() => {
        loginButton.click();
      });

      expect(screen.getByTestId('user-data')).toHaveTextContent(JSON.stringify(mockUserData));

      // Logout
      act(() => {
        logoutButton.click();
      });

      expect(screen.getByTestId('user-data')).toHaveTextContent('No user');
      expect(screen.getByTestId('user-id')).toHaveTextContent('No ID');
      expect(screen.getByTestId('username')).toHaveTextContent('No username');
      expect(screen.getByTestId('email')).toHaveTextContent('No email');
      expect(screen.getByTestId('first-name')).toHaveTextContent('No firstName');
      expect(screen.getByTestId('last-name')).toHaveTextContent('No lastName');
    });
  });

  describe('Multiple Components Using Hook', () => {
    it('nên share state giữa multiple components sử dụng useAuth', () => {
      const Component1: React.FC = () => {
        const auth = useAuth();
        return (
          <div>
            <span data-testid="component1-user">
              {auth.user?.username || 'No user'}
            </span>
            <button 
              data-testid="component1-login"
              onClick={() => auth.login(mockUserData)}
            >
              Login from Component 1
            </button>
          </div>
        );
      };

      const Component2: React.FC = () => {
        const auth = useAuth();
        return (
          <div>
            <span data-testid="component2-user">
              {auth.user?.email || 'No email'}
            </span>
            <button 
              data-testid="component2-logout"
              onClick={() => auth.logout()}
            >
              Logout from Component 2
            </button>
          </div>
        );
      };

      render(
        <AuthProvider>
          <Component1 />
          <Component2 />
        </AuthProvider>
      );

      // Initial state
      expect(screen.getByTestId('component1-user')).toHaveTextContent('No user');
      expect(screen.getByTestId('component2-user')).toHaveTextContent('No email');

      // Login từ component 1
      act(() => {
        screen.getByTestId('component1-login').click();
      });

      // Both components should see the updated state
      expect(screen.getByTestId('component1-user')).toHaveTextContent('testuser');
      expect(screen.getByTestId('component2-user')).toHaveTextContent('<EMAIL>');

      // Logout từ component 2
      act(() => {
        screen.getByTestId('component2-logout').click();
      });

      // Both components should see the cleared state
      expect(screen.getByTestId('component1-user')).toHaveTextContent('No user');
      expect(screen.getByTestId('component2-user')).toHaveTextContent('No email');
    });
  });

  describe('Edge Cases and Error Scenarios', () => {
    it('nên handle multiple consecutive login calls', () => {
      const { result } = renderHook(() => useAuth(), {
        wrapper: AuthProviderWrapper,
      });

      const user1: UserModel = { ...mockUserData, id: 1, username: 'user1' };
      const user2: UserModel = { ...mockUserData, id: 2, username: 'user2' };
      const user3: UserModel = { ...mockUserData, id: 3, username: 'user3' };

      act(() => {
        result.current.login(user1);
        result.current.login(user2);
        result.current.login(user3);
      });

      // Should contain the last user data
      expect(result.current.user).toEqual(user3);
      expect(result.current.user?.username).toBe('user3');
    });

    it('nên handle multiple consecutive logout calls', () => {
      const { result } = renderHook(() => useAuth(), {
        wrapper: AuthProviderWrapper,
      });

      // Login trước
      act(() => {
        result.current.login(mockUserData);
      });

      expect(result.current.user).toEqual(mockUserData);

      // Multiple logout calls
      act(() => {
        result.current.logout();
        result.current.logout();
        result.current.logout();
      });

      expect(result.current.user).toBeNull();
    });

    it('nên handle user data với missing optional fields', () => {
      const { result } = renderHook(() => useAuth(), {
        wrapper: AuthProviderWrapper,
      });

      const minimalUser: UserModel = {
        id: 1,
        username: 'minimal',
        email: '<EMAIL>'
      };

      act(() => {
        result.current.login(minimalUser);
      });

      expect(result.current.user).toEqual(minimalUser);
      expect(result.current.user?.firstName).toBeUndefined();
      expect(result.current.user?.lastName).toBeUndefined();
    });

    it('nên maintain hook functionality với nested AuthProviders', () => {
      const InnerComponent: React.FC = () => {
        const auth = useAuth();
        return (
          <div data-testid="inner-component">
            {auth.user?.username || 'No user'}
          </div>
        );
      };

      render(
        <AuthProvider>
          <AuthProvider>
            <InnerComponent />
          </AuthProvider>
        </AuthProvider>
      );

      // Should use the closest provider (inner one)
      expect(screen.getByTestId('inner-component')).toHaveTextContent('No user');
    });
  });

  describe('TypeScript Type Safety', () => {
    it('nên ensure type safety cho return values', () => {
      const { result } = renderHook(() => useAuth(), {
        wrapper: AuthProviderWrapper,
      });

      // TypeScript should infer correct types
      const authContext = result.current;
      
      // User should be UserModel | null
      if (authContext.user) {
        expect(typeof authContext.user.id).toBe('number');
        expect(typeof authContext.user.username).toBe('string');
        expect(typeof authContext.user.email).toBe('string');
        // Optional fields
        if (authContext.user.firstName) {
          expect(typeof authContext.user.firstName).toBe('string');
        }
        if (authContext.user.lastName) {
          expect(typeof authContext.user.lastName).toBe('string');
        }
      }

      // Functions should have correct signatures
      expect(authContext.login).toBeInstanceOf(Function);
      expect(authContext.logout).toBeInstanceOf(Function);
    });
  });
});