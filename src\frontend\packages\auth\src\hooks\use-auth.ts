import { useContext } from "react";

import { AuthContext, AuthContextType } from "../providers";

/**
 * Custom hook để sử dụng authentication context
 * <PERSON>ung cấp quyền truy cập vào auth state và functions
 * 
 * @returns AuthContextType - Object chứa auth state và methods
 * @throws Error nếu được sử dụng bên ngoài AuthProvider
 */
export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};
