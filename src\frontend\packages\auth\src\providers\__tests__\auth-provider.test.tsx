import React from 'react';
import { render, screen, fireEvent, act } from '@testing-library/react';
import { AuthProvider, AuthContext, AuthContextType } from '../auth-provider';
import { UserModel } from '../../types/models/user.model';

/**
 * Mock user data dùng cho các test case
 */
const mockUserData: UserModel = {
  id: 1,
  username: 'testuser',
  email: '<EMAIL>',
  firstName: 'Test',
  lastName: 'User'
};

/**
 * Component test helper để test context values
 */
const TestComponent: React.FC<{
  onContextValue?: (context: AuthContextType) => void;
}> = ({ onContextValue }) => {
  return (
    <AuthContext.Consumer>
      {(context) => {
        if (onContextValue && context) {
          onContextValue(context);
        }
        return (
          <div data-testid="test-component">
            <span data-testid="user-info">
              {context?.user ? JSON.stringify(context.user) : 'No user'}
            </span>
            <button 
              data-testid="login-btn" 
              onClick={() => context?.login(mockUserData)}
            >
              Login
            </button>
            <button 
              data-testid="logout-btn" 
              onClick={() => context?.logout()}
            >
              Logout
            </button>
          </div>
        );
      }}
    </AuthContext.Consumer>
  );
};

describe('AuthProvider', () => {
  describe('Component Rendering', () => {
    it('nên render children components một cách chính xác', () => {
      render(
        <AuthProvider>
          <div data-testid="child-component">Test Child</div>
        </AuthProvider>
      );

      expect(screen.getByTestId('child-component')).toBeInTheDocument();
      expect(screen.getByText('Test Child')).toBeInTheDocument();
    });

    it('nên render multiple children components', () => {
      render(
        <AuthProvider>
          <div data-testid="child-1">Child 1</div>
          <div data-testid="child-2">Child 2</div>
          <span data-testid="child-3">Child 3</span>
        </AuthProvider>
      );

      expect(screen.getByTestId('child-1')).toBeInTheDocument();
      expect(screen.getByTestId('child-2')).toBeInTheDocument();
      expect(screen.getByTestId('child-3')).toBeInTheDocument();
    });
  });

  describe('Context Creation and Initial State', () => {
    it('nên tạo context với initial state chính xác', () => {
      let contextValue: AuthContextType | null = null;

      render(
        <AuthProvider>
          <TestComponent onContextValue={(context) => { contextValue = context; }} />
        </AuthProvider>
      );

      expect(contextValue).not.toBeNull();
      expect(contextValue?.user).toBeNull();
      expect(typeof contextValue?.login).toBe('function');
      expect(typeof contextValue?.logout).toBe('function');
    });

    it('nên hiển thị "No user" khi chưa có user login', () => {
      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      );

      expect(screen.getByTestId('user-info')).toHaveTextContent('No user');
    });
  });

  describe('Login Function', () => {
    it('nên cập nhật user state khi gọi login function', () => {
      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      );

      const loginButton = screen.getByTestId('login-btn');
      
      act(() => {
        fireEvent.click(loginButton);
      });

      expect(screen.getByTestId('user-info')).toHaveTextContent(
        JSON.stringify(mockUserData)
      );
    });

    it('nên nhận và lưu trữ complete user data', () => {
      let contextValue: AuthContextType | null = null;

      render(
        <AuthProvider>
          <TestComponent onContextValue={(context) => { contextValue = context; }} />
        </AuthProvider>
      );

      act(() => {
        contextValue?.login(mockUserData);
      });

      expect(contextValue?.user).toEqual(mockUserData);
      expect(contextValue?.user?.id).toBe(1);
      expect(contextValue?.user?.username).toBe('testuser');
      expect(contextValue?.user?.email).toBe('<EMAIL>');
      expect(contextValue?.user?.firstName).toBe('Test');
      expect(contextValue?.user?.lastName).toBe('User');
    });

    it('nên xử lý user data với optional fields rỗng', () => {
      const userWithoutOptionalFields: UserModel = {
        id: 2,
        username: 'minimaluser',
        email: '<EMAIL>'
      };

      let contextValue: AuthContextType | null = null;

      render(
        <AuthProvider>
          <TestComponent onContextValue={(context) => { contextValue = context; }} />
        </AuthProvider>
      );

      act(() => {
        contextValue?.login(userWithoutOptionalFields);
      });

      expect(contextValue?.user).toEqual(userWithoutOptionalFields);
      expect(contextValue?.user?.firstName).toBeUndefined();
      expect(contextValue?.user?.lastName).toBeUndefined();
    });

    it('nên override previous user data khi login với user mới', () => {
      const firstUser: UserModel = {
        id: 1,
        username: 'firstuser',
        email: '<EMAIL>'
      };

      const secondUser: UserModel = {
        id: 2,
        username: 'seconduser',
        email: '<EMAIL>'
      };

      let contextValue: AuthContextType | null = null;

      render(
        <AuthProvider>
          <TestComponent onContextValue={(context) => { contextValue = context; }} />
        </AuthProvider>
      );

      // Login với user đầu tiên
      act(() => {
        contextValue?.login(firstUser);
      });

      expect(contextValue?.user).toEqual(firstUser);

      // Login với user thứ hai
      act(() => {
        contextValue?.login(secondUser);
      });

      expect(contextValue?.user).toEqual(secondUser);
      expect(contextValue?.user?.id).toBe(2);
      expect(contextValue?.user?.username).toBe('seconduser');
    });
  });

  describe('Logout Function', () => {
    it('nên clear user state khi gọi logout function', () => {
      let contextValue: AuthContextType | null = null;

      render(
        <AuthProvider>
          <TestComponent onContextValue={(context) => { contextValue = context; }} />
        </AuthProvider>
      );

      // Đăng nhập trước
      act(() => {
        contextValue?.login(mockUserData);
      });

      expect(contextValue?.user).toEqual(mockUserData);

      // Đăng xuất
      act(() => {
        contextValue?.logout();
      });

      expect(contextValue?.user).toBeNull();
    });

    it('nên hiển thị "No user" sau khi logout', () => {
      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      );

      const loginButton = screen.getByTestId('login-btn');
      const logoutButton = screen.getByTestId('logout-btn');

      // Login trước
      act(() => {
        fireEvent.click(loginButton);
      });

      expect(screen.getByTestId('user-info')).toHaveTextContent(
        JSON.stringify(mockUserData)
      );

      // Logout
      act(() => {
        fireEvent.click(logoutButton);
      });

      expect(screen.getByTestId('user-info')).toHaveTextContent('No user');
    });

    it('nên xử lý logout khi chưa có user (idempotent)', () => {
      let contextValue: AuthContextType | null = null;

      render(
        <AuthProvider>
          <TestComponent onContextValue={(context) => { contextValue = context; }} />
        </AuthProvider>
      );

      // Logout khi chưa có user
      expect(() => {
        act(() => {
          contextValue?.logout();
        });
      }).not.toThrow();

      expect(contextValue?.user).toBeNull();
    });
  });

  describe('Context Value Updates', () => {
    it('nên cung cấp context value được cập nhật cho tất cả consumers', () => {
      const Consumer1: React.FC = () => (
        <AuthContext.Consumer>
          {(context) => (
            <div data-testid="consumer-1">
              {context?.user?.username || 'No user'}
            </div>
          )}
        </AuthContext.Consumer>
      );

      const Consumer2: React.FC = () => (
        <AuthContext.Consumer>
          {(context) => (
            <div data-testid="consumer-2">
              {context?.user?.email || 'No email'}
            </div>
          )}
        </AuthContext.Consumer>
      );

      let contextValue: AuthContextType | null = null;

      render(
        <AuthProvider>
          <TestComponent onContextValue={(context) => { contextValue = context; }} />
          <Consumer1 />
          <Consumer2 />
        </AuthProvider>
      );

      // Initial state
      expect(screen.getByTestId('consumer-1')).toHaveTextContent('No user');
      expect(screen.getByTestId('consumer-2')).toHaveTextContent('No email');

      // After login
      act(() => {
        contextValue?.login(mockUserData);
      });

      expect(screen.getByTestId('consumer-1')).toHaveTextContent('testuser');
      expect(screen.getByTestId('consumer-2')).toHaveTextContent('<EMAIL>');

      // After logout
      act(() => {
        contextValue?.logout();
      });

      expect(screen.getByTestId('consumer-1')).toHaveTextContent('No user');
      expect(screen.getByTestId('consumer-2')).toHaveTextContent('No email');
    });

    it('nên maintain function references trong context value', () => {
      let contextValue1: AuthContextType | null = null;
      let contextValue2: AuthContextType | null = null;

      const { rerender } = render(
        <AuthProvider>
          <TestComponent onContextValue={(context) => { contextValue1 = context; }} />
        </AuthProvider>
      );

      rerender(
        <AuthProvider>
          <TestComponent onContextValue={(context) => { contextValue2 = context; }} />
        </AuthProvider>
      );

      // Functions should be stable references (same implementation)
      expect(typeof contextValue1?.login).toBe('function');
      expect(typeof contextValue1?.logout).toBe('function');
      expect(typeof contextValue2?.login).toBe('function');
      expect(typeof contextValue2?.logout).toBe('function');
    });
  });

  describe('Error Cases and Edge Cases', () => {
    it('nên xử lý empty children prop', () => {
      expect(() => {
        render(<AuthProvider>{null}</AuthProvider>);
      }).not.toThrow();
    });

    it('nên xử lý undefined children', () => {
      expect(() => {
        render(<AuthProvider>{undefined}</AuthProvider>);
      }).not.toThrow();
    });

    it('nên xử lý boolean children', () => {
      expect(() => {
        render(<AuthProvider>{true}</AuthProvider>);
      }).not.toThrow();

      expect(() => {
        render(<AuthProvider>{false}</AuthProvider>);
      }).not.toThrow();
    });

    it('nên xử lý string children', () => {
      render(<AuthProvider>Simple text child</AuthProvider>);
      expect(screen.getByText('Simple text child')).toBeInTheDocument();
    });

    it('nên xử lý array of children', () => {
      render(
        <AuthProvider>
          {[
            <div key="1" data-testid="array-child-1">Child 1</div>,
            <div key="2" data-testid="array-child-2">Child 2</div>
          ]}
        </AuthProvider>
      );

      expect(screen.getByTestId('array-child-1')).toBeInTheDocument();
      expect(screen.getByTestId('array-child-2')).toBeInTheDocument();
    });
  });

  describe('State Management', () => {
    it('nên isolate state giữa multiple AuthProvider instances', () => {
      const TestComponentForProvider1: React.FC = () => (
        <AuthContext.Consumer>
          {(context) => (
            <div>
              <span data-testid="provider-1-user">
                {context?.user?.username || 'No user'}
              </span>
              <button 
                data-testid="provider-1-login"
                onClick={() => context?.login({ ...mockUserData, username: 'user1' })}
              >
                Login Provider 1
              </button>
            </div>
          )}
        </AuthContext.Consumer>
      );

      const TestComponentForProvider2: React.FC = () => (
        <AuthContext.Consumer>
          {(context) => (
            <div>
              <span data-testid="provider-2-user">
                {context?.user?.username || 'No user'}
              </span>
              <button 
                data-testid="provider-2-login"
                onClick={() => context?.login({ ...mockUserData, username: 'user2' })}
              >
                Login Provider 2
              </button>
            </div>
          )}
        </AuthContext.Consumer>
      );

      render(
        <div>
          <AuthProvider>
            <TestComponentForProvider1 />
          </AuthProvider>
          <AuthProvider>
            <TestComponentForProvider2 />
          </AuthProvider>
        </div>
      );

      // Initial state
      expect(screen.getByTestId('provider-1-user')).toHaveTextContent('No user');
      expect(screen.getByTestId('provider-2-user')).toHaveTextContent('No user');

      // Login in provider 1
      act(() => {
        fireEvent.click(screen.getByTestId('provider-1-login'));
      });

      expect(screen.getByTestId('provider-1-user')).toHaveTextContent('user1');
      expect(screen.getByTestId('provider-2-user')).toHaveTextContent('No user');

      // Login in provider 2
      act(() => {
        fireEvent.click(screen.getByTestId('provider-2-login'));
      });

      expect(screen.getByTestId('provider-1-user')).toHaveTextContent('user1');
      expect(screen.getByTestId('provider-2-user')).toHaveTextContent('user2');
    });
  });
});