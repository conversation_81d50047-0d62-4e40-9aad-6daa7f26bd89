"use client";

import { createContext, useState, ReactNode } from "react";
import { UserModel } from "@workspace/auth/types/models";

/**
 * Interface định nghĩa type cho Authentication Context
 */
export interface AuthContextType {
  /** Thông tin user hiện tại (null nếu chưa login) */
  user: UserModel | null;
  /** Function để đăng nhập user */
  login: (user: UserModel) => void;
  /** Function để đăng xuất user */
  logout: () => void;
}

/**
 * React Context cho việc quản lý authentication state
 */
export const AuthContext = createContext<AuthContextType | undefined>(
  undefined
);

/**
 * AuthProvider component cung cấp authentication context cho toàn bộ ứng dụng
 * Quản lý state đăng nhập/đăng xuất của user
 * 
 * @param children - React components con sẽ có quyền truy cập auth context
 */
export const AuthProvider = ({ children }: { children: ReactNode }) => {
  /** State lưu trữ thông tin user hiện tại */
  const [user, setUser] = useState<UserModel | null>(null);

  /**
   * Function xử lý đăng nhập user
   * @param userData - Thông tin user để set vào state
   */
  const login = (userData: UserModel) => {
    setUser(userData);
    // In a real app, you'd set a cookie or token here
  };

  /**
   * Function xử lý đăng xuất user
   * Xóa thông tin user khỏi state
   */
  const logout = () => {
    setUser(null);
    // TODO: Clear authentication cookies or tokens to fully log out the user
  };

  return (
    <AuthContext.Provider value={{ user, login, logout }}>
      {children}
    </AuthContext.Provider>
  );
};
