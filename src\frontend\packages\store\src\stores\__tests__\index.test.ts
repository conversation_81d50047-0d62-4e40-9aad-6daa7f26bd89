/**
 * Unit tests cho Zustand stores
 * Test toàn bộ state management và actions
 * Mục tiêu: 100% LOC coverage và 75% branches coverage
 */

import { renderHook, act } from '@testing-library/react';
import { useCounterStore } from '../index';

// Mock zustand để có control tốt hơn trong testing
jest.mock('zustand', () => ({
  create: jest.fn()
}));

describe('Zustand Stores', () => {
  describe('useCounterStore', () => {
    let mockSet: jest.Mock;
    let store: any;

    beforeEach(() => {
      // Setup mock cho zustand create function
      mockSet = jest.fn();
      const { create } = require('zustand');
      
      // Mock zustand create để return test store
      create.mockImplementation((storeCreator: any) => {
        store = storeCreator(mockSet);
        return () => store;
      });

      // Reset mocks
      jest.clearAllMocks();
    });

    describe('initial state', () => {
      it('nên có initial count = 0', () => {
        const { create } = require('zustand');
        create.mockImplementation((storeCreator: any) => {
          const initialStore = storeCreator(mockSet);
          expect(initialStore.count).toBe(0);
          return () => initialStore;
        });

        require('../index');
      });

      it('nên có increment function', () => {
        const { create } = require('zustand');
        create.mockImplementation((storeCreator: any) => {
          const initialStore = storeCreator(mockSet);
          expect(typeof initialStore.increment).toBe('function');
          return () => initialStore;
        });

        require('../index');
      });

      it('nên có decrement function', () => {
        const { create } = require('zustand');
        create.mockImplementation((storeCreator: any) => {
          const initialStore = storeCreator(mockSet);
          expect(typeof initialStore.decrement).toBe('function');
          return () => initialStore;
        });

        require('../index');
      });
    });

    describe('increment action', () => {
      it('nên call set function với correct updater', () => {
        const { create } = require('zustand');
        create.mockImplementation((storeCreator: any) => {
          const store = storeCreator(mockSet);
          
          // Test increment function call
          store.increment();
          
          expect(mockSet).toHaveBeenCalledTimes(1);
          expect(mockSet).toHaveBeenCalledWith(expect.any(Function));
          
          return () => store;
        });

        require('../index');
      });

      it('nên increase count từ current state', () => {
        const { create } = require('zustand');
        create.mockImplementation((storeCreator: any) => {
          const store = storeCreator(mockSet);
          
          // Call increment
          store.increment();
          
          // Get the updater function passed to set
          const updaterFunction = mockSet.mock.calls[0][0];
          const mockCurrentState = { count: 5 };
          const newState = updaterFunction(mockCurrentState);
          
          expect(newState).toEqual({ count: 6 });
          
          return () => store;
        });

        require('../index');
      });

      it('nên handle increment từ 0', () => {
        const { create } = require('zustand');
        create.mockImplementation((storeCreator: any) => {
          const store = storeCreator(mockSet);
          
          store.increment();
          const updaterFunction = mockSet.mock.calls[0][0];
          const newState = updaterFunction({ count: 0 });
          
          expect(newState.count).toBe(1);
          
          return () => store;
        });

        require('../index');
      });

      it('nên handle increment từ negative numbers', () => {
        const { create } = require('zustand');
        create.mockImplementation((storeCreator: any) => {
          const store = storeCreator(mockSet);
          
          store.increment();
          const updaterFunction = mockSet.mock.calls[0][0];
          const newState = updaterFunction({ count: -5 });
          
          expect(newState.count).toBe(-4);
          
          return () => store;
        });

        require('../index');
      });

      it('nên handle multiple increment calls', () => {
        const { create } = require('zustand');
        create.mockImplementation((storeCreator: any) => {
          const store = storeCreator(mockSet);
          
          // Call increment 3 times
          store.increment();
          store.increment();
          store.increment();
          
          expect(mockSet).toHaveBeenCalledTimes(3);
          
          return () => store;
        });

        require('../index');
      });
    });

    describe('decrement action', () => {
      it('nên call set function với correct updater', () => {
        const { create } = require('zustand');
        create.mockImplementation((storeCreator: any) => {
          const store = storeCreator(mockSet);
          
          // Test decrement function call
          store.decrement();
          
          expect(mockSet).toHaveBeenCalledTimes(1);
          expect(mockSet).toHaveBeenCalledWith(expect.any(Function));
          
          return () => store;
        });

        require('../index');
      });

      it('nên decrease count từ current state', () => {
        const { create } = require('zustand');
        create.mockImplementation((storeCreator: any) => {
          const store = storeCreator(mockSet);
          
          // Call decrement
          store.decrement();
          
          // Get the updater function passed to set
          const updaterFunction = mockSet.mock.calls[0][0];
          const mockCurrentState = { count: 5 };
          const newState = updaterFunction(mockCurrentState);
          
          expect(newState).toEqual({ count: 4 });
          
          return () => store;
        });

        require('../index');
      });

      it('nên handle decrement từ 0', () => {
        const { create } = require('zustand');
        create.mockImplementation((storeCreator: any) => {
          const store = storeCreator(mockSet);
          
          store.decrement();
          const updaterFunction = mockSet.mock.calls[0][0];
          const newState = updaterFunction({ count: 0 });
          
          expect(newState.count).toBe(-1);
          
          return () => store;
        });

        require('../index');
      });

      it('nên handle decrement với positive numbers', () => {
        const { create } = require('zustand');
        create.mockImplementation((storeCreator: any) => {
          const store = storeCreator(mockSet);
          
          store.decrement();
          const updaterFunction = mockSet.mock.calls[0][0];
          const newState = updaterFunction({ count: 10 });
          
          expect(newState.count).toBe(9);
          
          return () => store;
        });

        require('../index');
      });

      it('nên handle multiple decrement calls', () => {
        const { create } = require('zustand');
        create.mockImplementation((storeCreator: any) => {
          const store = storeCreator(mockSet);
          
          // Call decrement 3 times
          store.decrement();
          store.decrement();
          store.decrement();
          
          expect(mockSet).toHaveBeenCalledTimes(3);
          
          return () => store;
        });

        require('../index');
      });
    });

    describe('combined actions', () => {
      it('nên handle alternating increment and decrement', () => {
        const { create } = require('zustand');
        create.mockImplementation((storeCreator: any) => {
          const store = storeCreator(mockSet);
          
          // Simulate alternating calls
          store.increment();
          store.decrement();
          store.increment();
          
          expect(mockSet).toHaveBeenCalledTimes(3);
          
          // Test each updater function
          let currentCount = 0;
          
          // First increment
          const updater1 = mockSet.mock.calls[0][0];
          currentCount = updater1({ count: currentCount }).count;
          expect(currentCount).toBe(1);
          
          // Then decrement
          const updater2 = mockSet.mock.calls[1][0];
          currentCount = updater2({ count: currentCount }).count;
          expect(currentCount).toBe(0);
          
          // Final increment
          const updater3 = mockSet.mock.calls[2][0];
          currentCount = updater3({ count: currentCount }).count;
          expect(currentCount).toBe(1);
          
          return () => store;
        });

        require('../index');
      });
    });

    describe('edge cases', () => {
      it('nên handle very large numbers', () => {
        const { create } = require('zustand');
        create.mockImplementation((storeCreator: any) => {
          const store = storeCreator(mockSet);
          
          store.increment();
          const updaterFunction = mockSet.mock.calls[0][0];
          const newState = updaterFunction({ count: Number.MAX_SAFE_INTEGER - 1 });
          
          expect(newState.count).toBe(Number.MAX_SAFE_INTEGER);
          
          return () => store;
        });

        require('../index');
      });

      it('nên handle very small numbers', () => {
        const { create } = require('zustand');
        create.mockImplementation((storeCreator: any) => {
          const store = storeCreator(mockSet);
          
          store.decrement();
          const updaterFunction = mockSet.mock.calls[0][0];
          const newState = updaterFunction({ count: Number.MIN_SAFE_INTEGER + 1 });
          
          expect(newState.count).toBe(Number.MIN_SAFE_INTEGER);
          
          return () => store;
        });

        require('../index');
      });

      it('nên preserve other state properties (không có trong current implementation)', () => {
        const { create } = require('zustand');
        create.mockImplementation((storeCreator: any) => {
          const store = storeCreator(mockSet);
          
          store.increment();
          const updaterFunction = mockSet.mock.calls[0][0];
          const mockState = { count: 5, otherProp: 'test' };
          const newState = updaterFunction(mockState);
          
          // Counter chỉ update count, không affect other properties
          expect(newState).toEqual({ count: 6 });
          
          return () => store;
        });

        require('../index');
      });
    });

    describe('type safety', () => {
      it('nên have correct TypeScript interface', () => {
        const { create } = require('zustand');
        create.mockImplementation((storeCreator: any) => {
          const store = storeCreator(mockSet);
          
          // Verify interface compliance
          expect(typeof store.count).toBe('number');
          expect(typeof store.increment).toBe('function');
          expect(typeof store.decrement).toBe('function');
          
          return () => store;
        });

        require('../index');
      });

      it('nên return void từ action functions', () => {
        const { create } = require('zustand');
        create.mockImplementation((storeCreator: any) => {
          const store = storeCreator(mockSet);
          
          const incrementResult = store.increment();
          const decrementResult = store.decrement();
          
          expect(incrementResult).toBeUndefined();
          expect(decrementResult).toBeUndefined();
          
          return () => store;
        });

        require('../index');
      });
    });

    describe('store creation', () => {
      it('nên call zustand create function', () => {
        const { create } = require('zustand');
        
        require('../index');
        
        expect(create).toHaveBeenCalledTimes(1);
        expect(create).toHaveBeenCalledWith(expect.any(Function));
      });

      it('nên return a hook function', () => {
        const { create } = require('zustand');
        const mockHook = jest.fn();
        create.mockReturnValue(mockHook);
        
        const { useCounterStore } = require('../index');
        
        expect(useCounterStore).toBe(mockHook);
      });
    });
  });

  // Test with real zustand implementation
  describe('integration tests with real zustand', () => {
    beforeEach(() => {
      jest.resetModules();
      jest.unmock('zustand');
    });

    it('nên work với real zustand implementation', () => {
      const { useCounterStore } = require('../index');
      
      const { result } = renderHook(() => useCounterStore());
      
      expect(result.current.count).toBe(0);
      expect(typeof result.current.increment).toBe('function');
      expect(typeof result.current.decrement).toBe('function');
    });

    it('nên update state correctly với real zustand', () => {
      const { useCounterStore } = require('../index');
      
      const { result } = renderHook(() => useCounterStore());
      
      // Test increment
      act(() => {
        result.current.increment();
      });
      
      expect(result.current.count).toBe(1);
      
      // Test decrement
      act(() => {
        result.current.decrement();
      });
      
      expect(result.current.count).toBe(0);
    });

    it('nên handle multiple operations với real zustand', () => {
      const { useCounterStore } = require('../index');
      
      const { result } = renderHook(() => useCounterStore());
      
      act(() => {
        result.current.increment();
        result.current.increment();
        result.current.decrement();
      });
      
      expect(result.current.count).toBe(1);
    });
  });

  afterEach(() => {
    jest.resetModules();
  });
});