// Local Jest setup file for @workspace/ui package
// Mock @testing-library/jest-dom functionality without importing

// Mock DOM APIs needed for testing
global.document = {
  createElement: jest.fn(() => ({
    setAttribute: jest.fn(),
    getAttribute: jest.fn(),
    appendChild: jest.fn(),
    classList: {
      add: jest.fn(),
      remove: jest.fn(),
      contains: jest.fn(),
    },
  })),
  createDocumentFragment: jest.fn(),
  getElementById: jest.fn(),
  querySelector: jest.fn(),
  querySelectorAll: jest.fn(() => []),
  addEventListener: jest.fn(),
  removeEventListener: jest.fn(),
};

global.window = {
  addEventListener: jest.fn(),
  removeEventListener: jest.fn(),
  getComputedStyle: jest.fn(() => ({})),
  matchMedia: jest.fn(() => ({
    matches: false,
    addListener: jest.fn(),
    removeListener: jest.fn(),
  })),
};

// Mock React DOM elements
global.HTMLElement = class MockHTMLElement {
  constructor() {
    this.style = {};
    this.classList = {
      add: jest.fn(),
      remove: jest.fn(),
      contains: jest.fn(),
      toggle: jest.fn(),
    };
    this.setAttribute = jest.fn();
    this.getAttribute = jest.fn();
    this.addEventListener = jest.fn();
    this.removeEventListener = jest.fn();
    this.focus = jest.fn();
    this.blur = jest.fn();
    this.click = jest.fn();
  }
};

global.HTMLButtonElement = class MockHTMLButtonElement extends global.HTMLElement {
  constructor() {
    super();
    this.disabled = false;
    this.type = 'button';
  }
};

global.Element = global.HTMLElement;